#!/bin/zsh
set -e  # Exit on error

# Configuration variables
REMOTE_HOST="*************"
REMOTE_USER="root"
REMOTE_PASS="ntvkHxhJKw9DrcmE"
REMOTE_PATH="/root/admin/build"
BUILD_DIR="build"

# Error handling function
handle_error() {
    echo "Error: $1"
    exit 1
}

# Check for required commands
check_command() {
    if ! command -v "$1" &> /dev/null; then
        handle_error "$1 is not installed. Install it using: brew install $2"
    fi
}

check_command "sshpass" "hudochenkov/sshpass/sshpass"
check_command "pv" "pv"

# Calculate total size of build directory for progress bar in a portable way
# Prefer GNU du if available, otherwise fall back to BSD du (macOS)
get_dir_size() {
    if du -sb "$BUILD_DIR" >/dev/null 2>&1; then
        # GNU coreutils du supports -b (bytes)
        du -sb "$BUILD_DIR" | cut -f1
    else
        # BSD du (macOS) does not support -b, use -sk (kibibytes) and convert to bytes
        du -sk "$BUILD_DIR" | awk '{print $1 * 1024}'
    fi
}

# Run build
echo "Starting build process..."
bun run build || handle_error "Build failed"
echo "Build completed successfully."

# Test SSH connection
echo "Testing SSH connection..."
sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH connection successful'" || handle_error "Failed to connect to remote server"

# Stop PM2 (if running)
echo "Stopping PM2..."
sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "pm2 stop 0 || echo 'PM2 process 0 not running'" || echo "Failed to connect or stop PM2"

# Remove remote build directory
echo "Removing remote build directory..."
sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "rm -rf $REMOTE_PATH/*" || handle_error "Failed to remove remote build directory"

# Copy files to remote server with progress bar
echo "Copying files to remote server..."
if [ -d "$BUILD_DIR" ]; then
    # Create a tar stream, pipe through pv, then extract on remote
    dir_size=$(get_dir_size)
    # "tar -c" without -f works on GNU tar but not on BSD tar (macOS). Use the portable form "tar -cf -".
    tar -cf - -C "$BUILD_DIR" . | pv -s "$dir_size" | \
        sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" \
        "mkdir -p $REMOTE_PATH && cd $REMOTE_PATH && tar -xf -" || handle_error "Failed to copy files"
else
    handle_error "Build directory does not exist"
fi
echo "Files copied successfully."

# Check PM2 status and restart
echo "Checking PM2 status..."
sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "pm2 list" || handle_error "Failed to check PM2 status"

echo "Restarting PM2..."
sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "pm2 flush && (pm2 restart 0 || pm2 start 0)" || {
    echo "PM2 restart failed. Checking PM2 logs and status..."
    sshpass -p "$REMOTE_PASS" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "pm2 list && pm2 logs --lines 10"
    handle_error "Failed to restart PM2"
}

echo "Deployment completed successfully!"