// import adapter from '@sveltejs/adapter-auto';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';
import adapter from 'svelte-adapter-bun';
import dotenv from 'dotenv';

dotenv.config();

/** @type {import('@sveltejs/kit').Config} */
const config = {
  // Consult https://kit.svelte.dev/docs/integrations#preprocessors
  // for more information about preprocessors
  preprocess: [
    vitePreprocess(),
    {
      name: 'strip-announcer',
      markup: ({ content: code }) => {
        code = code.replace(
          /<div id="svelte-announcer" [\s\S]*?<\/div>/,
          '<svelte:component this={null} />'
        );

        return { code };
      },
    },
  ],

  kit: {
    // adapter-auto only supports some environments, see https://kit.svelte.dev/docs/adapter-auto for a list.
    // If your environment is not supported or you settled on a specific environment, switch out the adapter.
    // See https://kit.svelte.dev/docs/adapters for more information about adapters.
    adapter: adapter({
      precompress: {
        brotli: true,
        gzip: true
      },
      minify: true,
      vitePreprocess: true,
    }),

    // Configure service worker
    serviceWorker: {
      register: true,
      files: (filepath) => !/\.DS_Store/.test(filepath),
    },

    // Disable SvelteKit's built-in CSRF protection (checkOrigin)
    csrf: {
      checkOrigin: false,
    },
  },
};

export default config;
