{"name": "phantom", "version": "0.0.2", "scripts": {"dev": "vite dev", "build": "vite build", "postbuild": "cp ecosystem.config.cjs build/ && cp .env build/.env && cp phantom-app-e3c03-firebase-adminsdk-fbsvc-63c302ea2c.json build/ && echo '.env' > build/.gitignore", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@eslint/js": "^9.30.1", "@supabase/supabase-js": "^2.39.7", "@sveltejs/adapter-auto": "6.0.1", "@sveltejs/kit": "2.21.1", "@sveltejs/vite-plugin-svelte": "5.0.3", "eslint": "^9.30.1", "eslint-plugin-svelte": "^3.10.1", "globals": "^16.3.0", "prettier": "^3.6.2", "svelte": "5.30.2", "svelte-adapter-bun": "^0.5.2", "svelte-check": "^4.2.2", "svelte-eslint-parser": "^1.2.0", "tslib": "^2.8.1", "vite": "6.3.5"}, "type": "module", "dependencies": {"axios": "^1.10.0", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "elysia": "1.1.26", "firebase-admin": "^13.4.0", "html-minifier": "^4.0.0", "qrcode": "^1.5.4", "querystring": "^0.2.1", "uuid": "^11.1.0"}}