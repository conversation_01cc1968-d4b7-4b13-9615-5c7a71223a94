#!/bin/bash

# Exit immediately on error, and treat unset variables as an error
set -euo pipefail

log() {
    echo "[INFO] $*"
}

error_exit() {
    echo "[ERROR] $*" >&2
    exit 1
}

# Check for required commands
for cmd in curl sudo apt-get file uname; do
    command -v $cmd >/dev/null 2>&1 || error_exit "$cmd is required but not installed."
done

log "Updating package list..."
sudo apt-get update -qq

log "Installing prerequisites..."
sudo apt-get install -y -qq curl libcurl4-openssl-dev

# Install Node.js LTS if not already installed
if ! command -v node >/dev/null 2>&1; then
    log "Installing Node.js LTS..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - >/dev/null
    sudo apt-get install -y -qq nodejs
else
    log "Node.js is already installed."
fi

# Install pm2 globally if not already installed
if ! command -v pm2 >/dev/null 2>&1; then
    log "Installing PM2..."
    sudo npm install -g pm2 >/dev/null
else
    log "PM2 is already installed."
fi

# Detect architecture
ARCH=$(uname -m)
log "Detected architecture: $ARCH"

# Download and verify binary
BINARY_URL="https://3geese.net/install/phantom-vpn-node"
BINARY_NAME="phantom-vpn-node"

log "Downloading binary from $BINARY_URL..."
curl -fsSL -H "X-Phantom-Auth: 3NZMA99jRDQA5Aa4xzZCip" -o "$BINARY_NAME" "$BINARY_URL"
chmod +x "$BINARY_NAME"

if ! file "$BINARY_NAME" | grep -q "executable"; then
    error_exit "Downloaded binary is not executable."
fi

cat <<'EOF'
  ___ _  _   _   _  _ _____ ___  __  __ 
 | _ \ || | /_\ | \| |_   _/ _ \|  \/  |
 |  _/ __ |/ _ \| .` | | || (_) | |\/| |
 |_| |_||_/_/ \_\_|\_|_|_|_\___/|_|__|_|
 \ \ / / _ \ \| | | \| |/ _ \|   \| __| 
  \ V /|  _/ .` | | .` | (_) | |) | _|  
   \_/ |_| |_|\_| |_|\_|\___/|___/|___| 
EOF

# Start the binary with pm2
log "Starting binary with PM2..."
pm2 start "./$BINARY_NAME" --name "phantom-vpn-node" -- -f /etc/wireguard/wg0.conf -v

# Enable PM2 startup
log "Enabling PM2 startup..."
pm2 startup -u "$USER" --hp "$HOME" >/dev/null
pm2 save

log "Setup complete. Application is running with PM2."
log "To view logs, run: pm2 logs"
