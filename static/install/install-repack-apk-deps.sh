#!/bin/bash

# Enable strict mode
set -euo pipefail
IFS=$'\n\t'

# Detect operating system
detect_os() {
    case "$(uname -s)" in
        Linux*)     
            if command -v apt-get >/dev/null 2>&1; then
                echo "debian"
            elif command -v yum >/dev/null 2>&1; then
                echo "redhat"
            elif command -v pacman >/dev/null 2>&1; then
                echo "arch"
            else
                echo "unknown"
            fi
            ;;
        Darwin*)    echo "macos";;
        *)          echo "unknown";;
    esac
}

# Function to check if a command is available
check_command() {
    if ! command -v "$1" >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

# Function to install packages based on OS
install_package() {
    local package="$1"
    case "$OS" in
        debian)
            sudo apt-get install -y "$package"
            ;;
        macos)
            if check_command brew; then
                brew install "$package"
            else
                echo "❌ Homebrew is required on macOS. Please install it from https://brew.sh/"
                exit 1
            fi
            ;;
        *)
            echo "❌ Unsupported operating system: $OS"
            exit 1
            ;;
    esac
}

# Function to update package lists
update_packages() {
    case "$OS" in
        debian)
            echo "🔄 Updating package lists..."
            sudo apt-get update
            ;;
        macos)
            echo "🔄 Updating Homebrew..."
            brew update
            ;;
    esac
}

# Function to get shell config file
get_shell_config() {
    if [[ "$SHELL" == */zsh ]]; then
        echo "$HOME/.zshrc"
    else
        echo "$HOME/.bashrc"
    fi
}

# Detect OS
OS=$(detect_os)
echo "🔍 Detected OS: $OS"

if [[ "$OS" == "unknown" ]]; then
    echo "❌ Unsupported operating system. This script supports Debian/Ubuntu and macOS."
    exit 1
fi

echo "📦 Installing dependencies for APK repacking on $OS..."

# Update package lists
update_packages

# Install Java (OpenJDK) for running JAR files
install_java() {
    case "$OS" in
        debian)
            install_package openjdk-17-jre
            ;;
        macos)
            # Try to install OpenJDK via Homebrew
            if ! brew list openjdk@17 >/dev/null 2>&1; then
                echo "📦 Installing OpenJDK@17 via Homebrew..."
                install_package openjdk@17
            fi
            
            # Detect Homebrew prefix (different for Intel vs Apple Silicon)
            BREW_PREFIX=$(brew --prefix)
            JAVA_HOME_PATH="$BREW_PREFIX/opt/openjdk@17"
            
            # Check if OpenJDK was installed successfully
            if [ ! -d "$JAVA_HOME_PATH" ]; then
                echo "❌ OpenJDK@17 installation failed"
                echo "ℹ️ Trying alternative installation methods..."
                
                # Try installing just 'openjdk' (latest version)
                if ! brew list openjdk >/dev/null 2>&1; then
                    echo "📦 Installing latest OpenJDK..."
                    install_package openjdk
                    JAVA_HOME_PATH="$BREW_PREFIX/opt/openjdk"
                fi
                
                # If still not found, try system Java
                if [ ! -d "$JAVA_HOME_PATH" ]; then
                    echo "ℹ️ Checking for system Java..."
                    if /usr/libexec/java_home >/dev/null 2>&1; then
                        JAVA_HOME_PATH=$(/usr/libexec/java_home)
                        echo "📦 Using system Java at: $JAVA_HOME_PATH"
                    else
                        echo "❌ No Java installation found"
                        echo "ℹ️ Please install Java manually:"
                        echo "   brew install openjdk@17"
                        echo "   or download from: https://adoptium.net/"
                        exit 1
                    fi
                fi
            fi
            
            # Link OpenJDK if it's a Homebrew installation
            if [[ "$JAVA_HOME_PATH" == *"/opt/homebrew/"* ]] || [[ "$JAVA_HOME_PATH" == *"/usr/local/"* ]]; then
                echo "🔗 Linking OpenJDK..."
                if [[ "$JAVA_HOME_PATH" == *"openjdk@17"* ]]; then
                    brew link openjdk@17 --force 2>/dev/null || true
                elif [[ "$JAVA_HOME_PATH" == *"openjdk"* ]]; then
                    brew link openjdk --force 2>/dev/null || true
                fi
            fi
            
            # Add JAVA_HOME to shell config and direct PATH to bin
            SHELL_CONFIG=$(get_shell_config)
            
            # Remove any existing JAVA_HOME/PATH entries to avoid duplicates
            if [ -f "$SHELL_CONFIG" ]; then
                grep -v "JAVA_HOME.*openjdk" "$SHELL_CONFIG" > "$SHELL_CONFIG.tmp" 2>/dev/null || true
                grep -v "PATH.*openjdk.*bin" "$SHELL_CONFIG.tmp" > "$SHELL_CONFIG" 2>/dev/null || true
                rm -f "$SHELL_CONFIG.tmp"
            fi
            
            # Add the correct PATH configuration
            if [[ "$JAVA_HOME_PATH" == *"openjdk@17"* ]]; then
                echo "export JAVA_HOME=$JAVA_HOME_PATH" >> "$SHELL_CONFIG"
                echo "export PATH=\"$JAVA_HOME_PATH/bin:\$PATH\"" >> "$SHELL_CONFIG"
            elif [[ "$JAVA_HOME_PATH" == *"openjdk"* ]]; then
                echo "export JAVA_HOME=$JAVA_HOME_PATH" >> "$SHELL_CONFIG"
                echo "export PATH=\"$JAVA_HOME_PATH/bin:\$PATH\"" >> "$SHELL_CONFIG"
            fi
            
            # Set for current session
            export JAVA_HOME="$JAVA_HOME_PATH"
            export PATH="$JAVA_HOME_PATH/bin:$PATH"
            
            # Source the shell config to apply changes immediately
            echo "🔄 Applying shell configuration..."
            source "$SHELL_CONFIG" 2>/dev/null || true
            ;;
    esac
}

# Check Java installation and version
check_java_version() {
    if check_command java; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        echo "✅ Java version: $JAVA_VERSION"
        return 0
    else
        return 1
    fi
}

if ! check_java_version; then
    echo "📦 Installing OpenJDK..."
    install_java
    
    # Verify installation
    if ! check_java_version; then
        echo "❌ Java installation failed or not properly configured"
        case "$OS" in
            macos)
                BREW_PREFIX=$(brew --prefix)
                echo "ℹ️ Manual steps to fix Java:"
                echo "   1. Install Java: brew install openjdk@17"
                echo "   2. Link it: brew link openjdk@17 --force"
                echo "   3. Add to your shell config (~/.zshrc or ~/.bashrc):"
                echo "      export JAVA_HOME=$BREW_PREFIX/opt/openjdk@17"
                echo "      export PATH=\$JAVA_HOME/bin:\$PATH"
                echo "   4. Reload shell: source ~/.zshrc"
                ;;
        esac
        exit 1
    fi
else
    echo "✅ Java is already installed"
fi

# Remove outdated apktool or aapt if installed via package manager
case "$OS" in
    debian)
        if check_command apktool || check_command aapt; then
            echo "🗑️ Removing outdated apktool/aapt from apt-get..."
            sudo apt-get remove -y apktool aapt 2>/dev/null || true
        fi
        ;;
    macos)
        if brew list apktool >/dev/null 2>&1; then
            echo "🗑️ Removing outdated apktool from Homebrew..."
            brew uninstall apktool 2>/dev/null || true
        fi
        ;;
esac

# Install apktool 2.11.1 from official release
if ! check_command apktool || [ "$(apktool --version 2>/dev/null)" != "2.11.1" ]; then
    echo "📦 Installing apktool 2.11.1..."
    case "$OS" in
        debian)
            sudo wget https://github.com/iBotPeaches/Apktool/releases/download/v2.11.1/apktool_2.11.1.jar -O /usr/local/bin/apktool.jar
            sudo wget https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/linux/apktool -O /usr/local/bin/apktool
            sudo chmod +x /usr/local/bin/apktool
            ;;
        macos)
            # Create local bin directory if it doesn't exist
            sudo mkdir -p /usr/local/bin
            sudo curl -L https://github.com/iBotPeaches/Apktool/releases/download/v2.11.1/apktool_2.11.1.jar -o /usr/local/bin/apktool.jar
            sudo curl -L https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/osx/apktool -o /usr/local/bin/apktool
            sudo chmod +x /usr/local/bin/apktool
            ;;
    esac
else
    echo "✅ apktool 2.11.1 is already installed"
fi

# Install Android SDK build-tools for aapt/aapt2
if ! check_command aapt; then
    echo "📦 Installing Android SDK build-tools (aapt/aapt2)..."
    case "$OS" in
        debian)
            sudo mkdir -p /opt/android-sdk/build-tools
            wget https://dl.google.com/android/repository/build-tools_r34-linux.zip -O /tmp/build-tools_r34.zip
            sudo unzip /tmp/build-tools_r34.zip -d /opt/android-sdk/build-tools
            # Find the actual extracted directory (it might be android-14 or similar)
            EXTRACTED_DIR=$(find /opt/android-sdk/build-tools -name "aapt" -type f | head -1 | xargs dirname)
            if [ -n "$EXTRACTED_DIR" ]; then
                sudo chmod +x "$EXTRACTED_DIR/aapt" "$EXTRACTED_DIR/aapt2"
                # Add build-tools to PATH
                SHELL_CONFIG=$(get_shell_config)
                if ! grep -q "$EXTRACTED_DIR" "$SHELL_CONFIG" 2>/dev/null; then
                    echo "export PATH=\$PATH:$EXTRACTED_DIR" >> "$SHELL_CONFIG"
                fi
                export PATH=$PATH:$EXTRACTED_DIR
            else
                echo "❌ Could not find aapt in extracted build-tools"
                exit 1
            fi
            rm /tmp/build-tools_r34.zip
            ;;
        macos)
            sudo mkdir -p /usr/local/android-sdk/build-tools
            curl -L https://dl.google.com/android/repository/build-tools_r34-macosx.zip -o /tmp/build-tools_r34.zip
            sudo unzip /tmp/build-tools_r34.zip -d /usr/local/android-sdk/build-tools
            # Find the actual extracted directory (it might be android-14 or similar)
            EXTRACTED_DIR=$(find /usr/local/android-sdk/build-tools -name "aapt" -type f | head -1 | xargs dirname)
            if [ -n "$EXTRACTED_DIR" ]; then
                sudo chmod +x "$EXTRACTED_DIR/aapt" "$EXTRACTED_DIR/aapt2"
                # Add build-tools to PATH
                SHELL_CONFIG=$(get_shell_config)
                if ! grep -q "$EXTRACTED_DIR" "$SHELL_CONFIG" 2>/dev/null; then
                    echo "export PATH=\$PATH:$EXTRACTED_DIR" >> "$SHELL_CONFIG"
                fi
                export PATH=$PATH:$EXTRACTED_DIR
            else
                echo "❌ Could not find aapt in extracted build-tools"
                exit 1
            fi
            rm /tmp/build-tools_r34.zip
            ;;
    esac
else
    echo "✅ aapt is already installed"
fi

# Install core utilities (realpath)
if ! check_command realpath; then
    echo "📦 Installing coreutils..."
    case "$OS" in
        debian)
            install_package coreutils
            ;;
        macos)
            install_package coreutils
            # On macOS, GNU coreutils are prefixed with 'g' by default
            # Create a symlink for realpath if it doesn't exist
            if ! check_command realpath && check_command grealpath; then
                ln -sf /usr/local/bin/grealpath /usr/local/bin/realpath
            fi
            ;;
    esac
else
    echo "✅ coreutils (realpath) is already installed"
fi

# Install bc for size calculations
if ! check_command bc; then
    echo "📦 Installing bc..."
    install_package bc
else
    echo "✅ bc is already installed"
fi

# Install wget if not available (mainly for macOS)
if ! check_command wget; then
    echo "📦 Installing wget..."
    case "$OS" in
        debian)
            install_package wget
            ;;
        macos)
            install_package wget
            ;;
    esac
else
    echo "✅ wget is already installed"
fi

# Install unzip if not available
if ! check_command unzip; then
    echo "📦 Installing unzip..."
    case "$OS" in
        debian)
            install_package unzip
            ;;
        macos)
            install_package unzip
            ;;
    esac
else
    echo "✅ unzip is already installed"
fi

# Verify installations
echo "🔍 Verifying installed dependencies..."

# Special Java verification with jar execution test
echo -n "🔍 Testing Java (jar execution): "
if check_command java; then
    # Test if Java can run jar files by testing with apktool
    if java -jar /usr/local/bin/apktool.jar --version >/dev/null 2>&1; then
        echo "✅ Java can execute jar files"
    else
        echo "❌ Java found but cannot execute jar files"
        case "$OS" in
            macos)
                echo "ℹ️ Try running these commands:"
                echo "   brew link openjdk@17 --force"
                echo "   export JAVA_HOME=/usr/local/opt/openjdk@17"
                echo "   export PATH=\$JAVA_HOME/bin:\$PATH"
                ;;
        esac
        exit 1
    fi
else
    echo "❌ Java not found in PATH"
    exit 1
fi

# Test other commands
for cmd in apktool aapt realpath bc wget unzip; do
    if check_command "$cmd"; then
        echo "✅ $cmd is installed"
    else
        echo "❌ Failed to install $cmd"
        exit 1
    fi
done

echo "✅ All dependencies for APK repacking have been installed successfully!"
echo "ℹ️ Note: You still need to provide signer.jar and a valid keystore file."

# Display appropriate shell config reload message
SHELL_CONFIG=$(get_shell_config)
echo "ℹ️ Shell configuration has been updated and sourced automatically."
echo "ℹ️ If you encounter issues, manually run: source $SHELL_CONFIG"
echo "ℹ️ Or open a new terminal session."