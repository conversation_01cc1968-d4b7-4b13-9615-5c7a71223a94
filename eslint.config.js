import globals from 'globals';
import js from '@eslint/js';
import svelte from 'eslint-plugin-svelte';
import svelteParser from 'svelte-eslint-parser';
import tsParser from '@typescript-eslint/parser';
import ts from '@typescript-eslint/eslint-plugin';

export default [
  {
    ignores: ['.svelte-kit/', 'build/'],
  },
  js.configs.recommended,
  {
    files: ['src/lib/server/**/*.js', 'src/routes/api/**/*.js'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.bun,
      },
    },
  },
  {
    files: ['src/**/*.js'],

    languageOptions: {
      globals: {
        ...globals.browser,
      },
    },
  },
  {
    files: ['**/*.svelte'],
    plugins: {
      svelte,
    },
    languageOptions: {
      parser: svelteParser,
      parserOptions: {
        parser: tsParser,
      },
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    rules: {
      ...svelte.configs.recommended.rules,
      'svelte/no-inner-declarations': 'off',
    },
  },
  {
    files: ['**/*.ts'],
    plugins: {
      ts,
    },
    languageOptions: {
      parser: tsParser,
      globals: {
        ...globals.node,
      },
    },
    rules: ts.configs.recommended.rules,
  },
];
