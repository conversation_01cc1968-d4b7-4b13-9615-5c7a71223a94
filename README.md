# Phantom Web Admin

Project consists of:
* admin dashboard
* nginx config
* install scripts for:
    * vpn node
    * apk repacking
* API for [billing app](https://github.com/3geeze/phantom-android-app)
    * FCM token registration
    * westwallet ipn
* API for [VPN node](https://github.com/3geeze/phantom-vpn-node)

## Script Installations

### Install on vpn node running wireguard

```shell
curl -sL https://3geese.net/install/phantom-vpn-node.sh | bash
```

### Install apk repacking dependencies

```shell
curl -sL https://3geese.net/install/install-repack-apk-deps.sh | bash
```

### Repack an apk example

```shell
./repack_apk.sh vpn.apk -f vpn.conf -n test.apk -k key.keystore -s signer.jar -a key0 -p 123321
```

## Tech Stack

*   **Framework:** [SvelteKit](https://kit.svelte.dev/)
*   **Bundler:** [Vite](https://vitejs.dev/)
*   **Package Manager:** [Bun](https://bun.sh/)
*   **Backend Integration:** [Supabase](https://supabase.io/)

## Getting Started

### Prerequisites

*   [Node.js](https://nodejs.org/) (v18 or higher)
*   [Bun](https://bun.sh/)

### Installation

1.  Clone the repository:
    ```bash
    git clone <repository-url>
    ```
2.  Navigate to the project directory:
    ```bash
    cd phantom-customer
    ```
3.  Install the dependencies:
    ```bash
    bun install
    ```
4.  Create a `.env` file by copying the example:
    ```bash
    cp .env.example .env
    ```
5.  Update the `.env` file with your credentials.

### Running in Development Mode

To start the development server, run:

```bash
bun run dev
```

The application will be available at `http://localhost:5173`.

## Available Scripts

*   `bun run dev`: Starts the development server.
*   `bun run build`: Creates a production-ready build of the application.
*   `bun run preview`: Starts a local server to preview the production build.

## Building for Production

To build the application for production, run:

```bash
bun run build
```

The build artifacts will be stored in the `build/` directory.