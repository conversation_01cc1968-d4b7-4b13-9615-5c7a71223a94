import { supabase } from '$lib/server/supabase.js';

/**
 * Delete a team and all its associated data
 * @param {string} teamId - The team ID to delete
 
 */
export async function deleteTeamWithAssociatedData(teamId) {
  console.log(`Starting deletion process for team: ${teamId}`);

  try {
    // Get team info for logging
    const { data: teamData } = await supabase
      .from('teams')
      .select('id, internal_id')
      .eq('id', teamId)
      .single();

    if (!teamData) {
      return { success: false, error: 'Team not found' };
    }

    // Delete all wallets associated with this team
    console.log(`Deleting wallets for team: ${teamId}`);
    const { error: walletsError, count: walletsCount } = await supabase
      .from('wallets')
      .delete({ count: 'exact' })
      .eq('team_id', teamId);

    if (walletsError) {
      console.error(`Error deleting wallets for team ${teamId}:`, walletsError);
      return {
        success: false,
        error: `Failed to delete wallets: ${walletsError.message}`,
      };
    }
    console.log(`Deleted ${walletsCount || 0} wallets for team ${teamId}`);

    // Delete all transactions associated with this team
    console.log(`Deleting transactions for team: ${teamId}`);
    const { error: transactionsError, count: transactionsCount } =
      await supabase
        .from('transactions')
        .delete({ count: 'exact' })
        .eq('team_id', teamId);

    if (transactionsError) {
      console.error(
        `Error deleting transactions for team ${teamId}:`,
        transactionsError
      );
      return {
        success: false,
        error: `Failed to delete transactions: ${transactionsError.message}`,
      };
    }
    console.log(
      `Deleted ${transactionsCount || 0} transactions for team ${teamId}`
    );

    // Delete all deposits associated with this team
    console.log(`Deleting deposits for team: ${teamId}`);
    const { error: depositsError, count: depositsCount } = await supabase
      .from('deposits')
      .delete({ count: 'exact' })
      .eq('team_id', teamId);

    if (depositsError) {
      console.error(
        `Error deleting deposits for team ${teamId}:`,
        depositsError
      );
      return {
        success: false,
        error: `Failed to delete deposits: ${depositsError.message}`,
      };
    }
    console.log(`Deleted ${depositsCount || 0} deposits for team ${teamId}`);

    // Delete all devices associated with this team
    console.log(`Deleting devices for team: ${teamId}`);
    const { error: devicesError, count: devicesCount } = await supabase
      .from('devices')
      .delete({ count: 'exact' })
      .eq('team_id', teamId);

    if (devicesError) {
      console.error(`Error deleting devices for team ${teamId}:`, devicesError);
      return {
        success: false,
        error: `Failed to delete devices: ${devicesError.message}`,
      };
    }
    console.log(`Deleted ${devicesCount || 0} devices for team ${teamId}`);

    // Finally, delete the team itself
    console.log(`Deleting team: ${teamId}`);
    const { error: teamError } = await supabase
      .from('teams')
      .delete()
      .eq('id', teamId);

    if (teamError) {
      console.error(`Error deleting team ${teamId}:`, teamError);
      return {
        success: false,
        error: `Failed to delete team: ${teamError.message}`,
      };
    }

    console.log(
      `Successfully deleted team ${teamId} and all associated data:`,
      {
        wallets: walletsCount || 0,
        transactions: transactionsCount || 0,
        deposits: depositsCount || 0,
        devices: devicesCount || 0,
      }
    );

    return { success: true };
  } catch (error) {
    console.error(
      `Unexpected error during team deletion for ${teamId}:`,
      error
    );
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: `Unexpected error: ${errorMessage}` };
  }
}

/**
 * Get team statistics (count of associated records)
 * @param {string} teamId - The team ID
 
 */
export async function getTeamStatistics(teamId) {
  try {
    // Get counts for all associated data
    const [walletsResult, transactionsResult, depositsResult, devicesResult] =
      await Promise.all([
        supabase
          .from('wallets')
          .select('*', { count: 'exact', head: true })
          .eq('team_id', teamId),
        supabase
          .from('transactions')
          .select('*', { count: 'exact', head: true })
          .eq('team_id', teamId),
        supabase
          .from('deposits')
          .select('*', { count: 'exact', head: true })
          .eq('team_id', teamId),
        supabase
          .from('devices')
          .select('*', { count: 'exact', head: true })
          .eq('team_id', teamId),
      ]);

    return {
      wallets: walletsResult.count || 0,
      transactions: transactionsResult.count || 0,
      deposits: depositsResult.count || 0,
      devices: devicesResult.count || 0,
    };
  } catch (error) {
    console.error(`Error getting team statistics for ${teamId}:`, error);
    return {
      wallets: 0,
      transactions: 0,
      deposits: 0,
      devices: 0,
    };
  }
}
