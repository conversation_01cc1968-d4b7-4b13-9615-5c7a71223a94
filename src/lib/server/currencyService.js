// Currency conversion service for cryptocurrency to USD conversion
import { env } from '$env/dynamic/private';
import axios from 'axios';

// CoinMarketCap API configuration
const CMC_API_KEY = env.CMC_API_KEY;
const CMC_BASE_URL = 'https://pro-api.coinmarketcap.com/v1';

// Fallback exchange rates (used when API is unavailable)
const FALLBACK_RATES = {
  USDTTRC: 1.0,
  USDT: 1.0,
  BTC: 102000,
  ETH: 2270,
  TRX: 0.08,
  TON: 2.5,
  SOL: 173.5,
  NOT: 0.01,
  XMR: 150,
  XRP: 2.04,
  DOGE: 0.08,
};

// Type for supported currencies
const SUPPORTED_CURRENCIES = Object.keys(FALLBACK_RATES);

/**
 * Get exchange rate for a cryptocurrency to USD
 * @param {string} currency - Currency code (e.g., 'BTC', 'USDTTRC')
 * @returns {Promise<number>} Exchange rate to USD
 */
export async function getExchangeRate(currency) {
  try {
    // For stablecoins pegged to USD, return 1.0
    if (currency === 'USDTTRC' || currency === 'USDT' || currency === 'USDC') {
      return 1.0;
    }

    // Try to get real-time rates from an API (you can implement this)
    const realTimeRate = await getRealTimeRate(currency);
    if (realTimeRate) {
      return realTimeRate;
    }

    // Fall back to hardcoded rates
    const fallbackRate = FALLBACK_RATES[currency];
    if (fallbackRate !== undefined) {
      console.log(
        `[CurrencyService] Using fallback rate for ${currency}: ${fallbackRate} USD`
      );
      return fallbackRate;
    }

    // If no rate found, log warning and return 0
    console.warn(
      `[CurrencyService] No exchange rate found for currency: ${currency}`
    );
    return 0;
  } catch (error) {
    console.error(
      `[CurrencyService] Error getting exchange rate for ${currency}:`,
      error
    );
    const fallbackRate = FALLBACK_RATES[currency];
    return fallbackRate !== undefined ? fallbackRate : 0;
  }
}

/**
 * Convert cryptocurrency amount to USD
 * @param {number} amount - Amount in cryptocurrency
 * @param {string} currency - Currency code
 * @returns {Promise<number>} Amount in USD
 */
export async function convertToUSD(amount, currency) {
  try {
    const rate = await getExchangeRate(currency);
    const usdAmount = amount * rate;

    console.log(
      `[CurrencyService] Converting ${amount} ${currency} to USD: ${usdAmount} (rate: ${rate})`
    );
    return usdAmount;
  } catch (error) {
    console.error(
      `[CurrencyService] Error converting ${amount} ${currency} to USD:`,
      error
    );
    return 0;
  }
}

/**
 * Get real-time exchange rate from CoinMarketCap API
 * @param {string} currency - Currency code
 * @returns {Promise<number|null>} Exchange rate or null if failed
 */
async function getRealTimeRate(currency) {
  try {
    // Skip API call if no API key is configured
    if (!CMC_API_KEY) {
      console.log(
        `[CurrencyService] No CMC API key configured, using fallback rates`
      );
      return null;
    }

    // Get CoinMarketCap symbol for the currency
    const cmcSymbol = getCoinMarketCapSymbol(currency);
    if (!cmcSymbol) {
      console.log(
        `[CurrencyService] No CoinMarketCap mapping found for ${currency}`
      );
      return null;
    }

    // Make API request to CoinMarketCap
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: cmcSymbol,
          convert: 'USD',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    if (response.data && response.data.data && response.data.data[cmcSymbol]) {
      const price = response.data.data[cmcSymbol].quote.USD.price;
      console.log(
        `[CurrencyService] Got real-time rate for ${currency} (${cmcSymbol}): ${price} USD`
      );
      return price;
    }

    console.warn(
      `[CurrencyService] No price data found for ${currency} (${cmcSymbol}) in CMC response`
    );
    return null;
  } catch (error) {
    // Simplified error handling
    console.error(
      `[CurrencyService] Error fetching real-time rate for ${currency}:`,
      error
    );
    return null;
  }
}

/**
 * Map currency codes to CoinMarketCap symbols
 * @param {string} currency - Currency code
 * @returns {string|null} CoinMarketCap symbol or null if not supported
 */
function getCoinMarketCapSymbol(currency) {
  const mapping = {
    BTC: 'BTC',
    ETH: 'ETH',
    TRX: 'TRX',
    TON: 'TON',
    SOL: 'SOL',
    XMR: 'XMR',
    XRP: 'XRP',
    DOGE: 'DOGE',
    USDTTRC: 'USDT', // USDT TRC-20 maps to USDT symbol
    USDT: 'USDT',
    NOT: 'NOT', // Notcoin
  };

  return mapping[currency] || null;
}

/**
 * Update exchange rates from CoinMarketCap API (can be called periodically)
 * @returns {Promise<Object>} Updated rates object
 */
export async function updateExchangeRates() {
  try {
    console.log(
      '[CurrencyService] Updating exchange rates from CoinMarketCap...'
    );

    if (!CMC_API_KEY) {
      console.log(
        '[CurrencyService] No CMC API key configured, skipping rate update'
      );
      return FALLBACK_RATES;
    }

    // Get all supported symbols for batch request
    const symbols = Object.keys(FALLBACK_RATES)
      .map((currency) => getCoinMarketCapSymbol(currency))
      .filter((symbol) => symbol !== null)
      .join(',');

    if (!symbols) {
      console.log('[CurrencyService] No supported symbols found for CMC API');
      return FALLBACK_RATES;
    }

    // Make batch API request to CoinMarketCap
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: symbols,
          convert: 'USD',
        },
        timeout: 15000, // 15 second timeout for batch request
      }
    );

    const updatedRates = { ...FALLBACK_RATES };
    let updateCount = 0;

    if (response.data && response.data.data) {
      // Update rates with API data
      for (const currency of Object.keys(FALLBACK_RATES)) {
        const cmcSymbol = getCoinMarketCapSymbol(currency);
        if (cmcSymbol && response.data.data[cmcSymbol]) {
          const price = response.data.data[cmcSymbol].quote.USD.price;
          updatedRates[currency] = price;
          updateCount++;
          console.log(`[CurrencyService] Updated ${currency}: ${price} USD`);
        }
      }
    }

    console.log(
      `[CurrencyService] Successfully updated ${updateCount} exchange rates`
    );
    return updatedRates;
  } catch (error) {
    console.error('[CurrencyService] Error updating exchange rates:', error);
    return FALLBACK_RATES;
  }
}

/**
 * Get all supported currencies and their current rates
 
 */
export async function getAllRates() {
  try {
    const rates = {};

    for (const currency of Object.keys(FALLBACK_RATES)) {
      rates[currency] = await getExchangeRate(currency);
    }

    return rates;
  } catch (error) {
    console.error('[CurrencyService] Error getting all rates:', error);
    return { ...FALLBACK_RATES };
  }
}

/**
 * Test CoinMarketCap API connection
 * @returns {Promise<Object>} Test results
 */
export async function testCoinMarketCapAPI() {
  try {
    console.log('[CurrencyService] Testing CoinMarketCap API connection...');

    if (!CMC_API_KEY) {
      return {
        success: false,
        error: 'No CMC_API_KEY environment variable configured',
        suggestion:
          'Set CMC_API_KEY environment variable with your CoinMarketCap API key',
      };
    }

    // Test with a simple BTC price request
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: 'BTC',
          convert: 'USD',
        },
        timeout: 10000,
      }
    );

    if (response.data && response.data.data && response.data.data.BTC) {
      const btcPrice = response.data.data.BTC.quote.USD.price;
      return {
        success: true,
        message: 'CoinMarketCap API connection successful',
        testPrice: `BTC: $${btcPrice.toFixed(2)}`,
        apiKeyConfigured: true,
      };
    } else {
      return {
        success: false,
        error: 'Unexpected response format from CoinMarketCap API',
        response: response.data,
      };
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      error: 'Failed to connect to CoinMarketCap API',
      details: errorMessage,
      apiKeyConfigured: !!CMC_API_KEY,
    };
  }
}
