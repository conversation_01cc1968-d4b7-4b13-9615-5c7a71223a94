/**
 * APK Repacker Service
 *
 * This service provides functionality for repacking APK files with custom configurations.
 * It handles file validation, job management, and the repacking process.
 */
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { fileURLToPath } from 'url';

// Get the directory name equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const execPromise = promisify(exec);

// Constants
const TMP_DIR = path.join(process.cwd(), 'tmp_apk_repacker');

// Determine if we're in development or production mode
const isDev = process.env.NODE_ENV === 'development';

// Helper function to find the _repack_apk directory
function findRepackApkDir() {
  // Path to the directory containing assets for the repacking script.
  // In development, it's in the project root. In production, the Vite build
  // process copies it to `server/_repack_apk` relative to this script.
  const prodPath = path.resolve(__dirname, '_repack_apk');
  const devPath = path.resolve(process.cwd(), '_repack_apk');

  if (fs.existsSync(prodPath)) {
    console.log(`[APK Repacker] Found _repack_apk directory at: ${prodPath}`);
    return prodPath;
  } else if (fs.existsSync(devPath)) {
    console.log(`[APK Repacker] Found _repack_apk directory at: ${devPath}`);
    return devPath;
  }

  const errorMsg = `[APK Repacker] Critical: _repack_apk directory not found. Checked: ${prodPath} and ${devPath}`;
  console.error(errorMsg);
  throw new Error(errorMsg);
}

// Lazy-loaded paths to avoid file system operations during module import
let REPACK_APK_DIR = null;
let REPACK_SCRIPT = null;

// Function to initialize paths when needed
function initializePaths() {
  if (REPACK_APK_DIR === null) {
    REPACK_APK_DIR = findRepackApkDir();
    REPACK_SCRIPT = path.join(REPACK_APK_DIR, 'repack_apk.sh');
  }
  return { REPACK_APK_DIR, REPACK_SCRIPT };
}

// Maximum age of temporary files
const MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const ONE_HOUR = 60 * 60 * 1000; // 1 hour in milliseconds
const FIVE_MINUTES = 5 * 60 * 1000; // 5 minutes in milliseconds

// Note: Initialization is now lazy-loaded to avoid file system operations during module import

/**
 * Clean up old temporary files
 
 * @returns {number} Number of directories removed
 */
function cleanupTempFiles(maxAgeMs = MAX_AGE) {
  try {
    // Create temp directory if it doesn't exist
    if (!fs.existsSync(TMP_DIR)) {
      fs.mkdirSync(TMP_DIR, { recursive: true });
      return 0;
    }

    const now = Date.now();
    let removedCount = 0;

    // Read all directories in the temp directory
    const dirs = fs.readdirSync(TMP_DIR);

    for (const dir of dirs) {
      try {
        // Skip non-numeric directories (job IDs are timestamps)
        if (!/^\d+$/.test(dir)) {
          continue;
        }

        const dirPath = path.join(TMP_DIR, dir);
        const stats = fs.statSync(dirPath);

        // Check if the directory is older than the specified age
        if (now - stats.mtimeMs > maxAgeMs) {
          // Remove the directory and its contents
          fs.rmSync(dirPath, { recursive: true, force: true });
          removedCount++;
          console.log(`Removed old APK repacking directory: ${dir}`);
        }
      } catch (err) {
        console.error(`Error processing directory ${dir}:`, err);
      }
    }

    return removedCount;
  } catch (err) {
    console.error('Error cleaning up temporary files:', err);
    return 0;
  }
}

/**
 * Clean up completed job directories that are older than 1 hour
 * @returns {number} Number of directories removed
 */
function cleanupCompletedJobs() {
  // Use a shorter time period (1 hour) for completed jobs
  return cleanupTempFiles(ONE_HOUR);
}

/**
 * Clean up all job directories except the most recent ones (last 5 minutes)
 * This is more aggressive and should be used with caution
 * @returns {number} Number of directories removed
 */
function forceCleanupAllExceptRecent() {
  // Only keep jobs from the last 5 minutes
  return cleanupTempFiles(FIVE_MINUTES);
}

/**
 * Validate an APK file
 * @param {File|Buffer} apkFile - The APK file to validate
 
 */
async function validateApkFile(apkFile) {
  try {
    // Convert to buffer if it's a File object
    let apkBuffer;
    if (apkFile instanceof Buffer) {
      apkBuffer = apkFile;
    } else {
      // Assume it's a File object
      apkBuffer = Buffer.from(await apkFile.arrayBuffer());
    }

    // Check if the file is a valid ZIP file (APK files are ZIP files)
    // ZIP files start with the magic bytes 'PK\x03\x04'
    if (
      apkBuffer.length < 4 ||
      apkBuffer[0] !== 0x50 || // 'P'
      apkBuffer[1] !== 0x4b || // 'K'
      apkBuffer[2] !== 0x03 ||
      apkBuffer[3] !== 0x04
    ) {
      return {
        isValid: false,
        error: 'The file is not a valid APK file',
        details: 'The file does not have the correct APK/ZIP format signature.',
      };
    }

    return { isValid: true };
  } catch (err) {
    return {
      isValid: false,
      error: 'Error validating APK file',
      details: err instanceof Error ? err.message : String(err),
    };
  }
}

/**
 * Create a new APK repacking job
 * @param {File|Buffer} apkFile - The APK file to repack
 * @param {string} config - The configuration to embed in the APK
 
 * @param {string} [customJobId] - Optional custom job ID (defaults to timestamp)
 
 */
async function createRepackingJob(
  apkFile,
  config,
  assetFilename = 'vpn.conf',
  customJobId
) {
  try {
    // Initialize paths if needed
    const { REPACK_APK_DIR: repackDir, REPACK_SCRIPT: repackScript } =
      initializePaths();

    // Start timing the repacking process
    const startTime = Date.now();

    // Create a unique job ID for this repacking job
    const jobId = customJobId || Date.now().toString();
    const jobDir = path.join(TMP_DIR, jobId);

    // Create job directory
    fs.mkdirSync(jobDir, { recursive: true });

    // Validate the APK file
    const validation = await validateApkFile(apkFile);
    if (!validation.isValid) {
      // Clean up the job directory
      try {
        fs.rmSync(jobDir, { recursive: true, force: true });
      } catch (err) {
        console.error(
          `Failed to clean up job directory: ${err instanceof Error ? err.message : String(err)}`
        );
      }

      return {
        success: false,
        error: validation.error,
        details: validation.details,
      };
    }

    // Save the uploaded APK file
    const apkPath = path.join(jobDir, 'input.apk');
    let apkBuffer;

    if (apkFile instanceof Buffer) {
      apkBuffer = apkFile;
    } else {
      // Assume it's a File object
      apkBuffer = Buffer.from(await apkFile.arrayBuffer());
    }

    fs.writeFileSync(apkPath, apkBuffer);

    // Save the configuration to a file with the specified asset filename
    // Sanitize the asset filename to prevent directory traversal
    const sanitizedAssetFilename = path.basename(assetFilename || 'vpn.conf');
    const configPath = path.join(jobDir, sanitizedAssetFilename);
    fs.writeFileSync(configPath, String(config));

    // Extract the base APK name from the input file
    let baseApkName = '';
    if (apkFile instanceof Buffer) {
      // If it's a buffer, check if it has a name property (added by the server)
      try {
        // @ts-ignore - Check for custom name property
        if (apkFile.name) {
          // @ts-ignore - Use the custom name property
          baseApkName = path.basename(apkFile.name, '.apk');
        } else {
          // Fallback to a generic name with timestamp
          baseApkName = `template-${Date.now().toString().slice(-6)}`;
        }
      } catch (err) {
        // Fallback to a generic name
        baseApkName = `template-${Date.now().toString().slice(-6)}`;
      }
    } else {
      // If it's a File object, get the name from the file
      try {
        // Remove .apk extension if present
        // @ts-ignore - File objects have a name property
        const fileName = apkFile.name || 'base';
        baseApkName = path.basename(fileName, '.apk');
      } catch (err) {
        // Fallback to a default name
        baseApkName = 'base';
      }
    }

    // Generate a short timestamp (last 4 digits of the job ID or current timestamp)
    const shortTimestamp = jobId.slice(-4) || Date.now().toString().slice(-4);

    // Create a new output APK name with the base name and timestamp
    const outputApkName = `${baseApkName}-${shortTimestamp}.apk`;
    const outputApkPath = path.join(jobDir, outputApkName);

    // Execute the repack_apk.sh script
    const keystore = path.join(repackDir, 'key.keystore');
    const signerJar = path.join(repackDir, 'signer.jar');

    // Verify that required files exist before proceeding
    if (!fs.existsSync(keystore)) {
      return {
        success: false,
        error: 'Keystore file not found',
        details: `The keystore file does not exist at: ${keystore}`,
      };
    }

    if (!fs.existsSync(signerJar)) {
      return {
        success: false,
        error: 'Signer JAR file not found',
        details: `The signer JAR file does not exist at: ${signerJar}`,
      };
    }

    if (!fs.existsSync(repackScript)) {
      return {
        success: false,
        error: 'Repack script not found',
        details: `The repack script does not exist at: ${repackScript}`,
      };
    }

    // Make sure the script is executable
    try {
      fs.chmodSync(repackScript, '755');
    } catch (err) {
      console.error(
        `Failed to make script executable: ${err instanceof Error ? err.message : String(err)}`
      );
    }

    // Create a unique temporary directory for this job to avoid conflicts
    // when multiple jobs run simultaneously
    const uniqueTmpDir = path.join(jobDir, 'tmp_apk');

    // Create the unique temporary directory
    try {
      fs.mkdirSync(uniqueTmpDir, { recursive: true });
      console.log(
        `[APK Repacker] Created unique temporary directory: ${uniqueTmpDir}`
      );
    } catch (err) {
      console.error(
        `[APK Repacker] Failed to create unique temporary directory: ${err.message}`
      );
    }

    // Pass the unique temporary directory to the script using the TMP_DIR environment variable
    const cmd = `TMP_DIR="${uniqueTmpDir}" ${repackScript} ${apkPath} -f ${configPath} -n ${outputApkName} -o ${jobDir} -k ${keystore} -s ${signerJar} -a key0 -p 123321 -d ${sanitizedAssetFilename}`;

    // Log the command (with password masked)
    console.log(
      `[APK Repacker] Executing command: ${cmd.replace('-p 123321', '-p ******').replace(uniqueTmpDir, 'UNIQUE_TMP_DIR')}`
    );

    // Verify all files exist before executing
    console.log(`[APK Repacker] Verifying files before execution:`);
    console.log(`- APK file: ${fs.existsSync(apkPath) ? 'EXISTS' : 'MISSING'}`);
    console.log(
      `- Config file: ${fs.existsSync(configPath) ? 'EXISTS' : 'MISSING'}`
    );
    console.log(
      `- Repack script: ${fs.existsSync(repackScript) ? 'EXISTS' : 'MISSING'}`
    );
    console.log(
      `- Keystore: ${fs.existsSync(keystore) ? 'EXISTS' : 'MISSING'}`
    );
    console.log(
      `- Signer JAR: ${fs.existsSync(signerJar) ? 'EXISTS' : 'MISSING'}`
    );
    console.log(
      `- Output directory: ${fs.existsSync(jobDir) ? 'EXISTS' : 'MISSING'}`
    );

    try {
      console.log(`[APK Repacker] Starting repacking process...`);
      const { stdout, stderr } = await execPromise(cmd);
      console.log(`[APK Repacker] Command completed`);

      // Log stdout and stderr (truncated if too long)
      if (stdout) {
        const truncatedStdout =
          stdout.length > 1000
            ? stdout.substring(0, 1000) + '...(truncated)'
            : stdout;
        console.log(`[APK Repacker] Command output: ${truncatedStdout}`);
      }

      if (stderr) {
        const truncatedStderr =
          stderr.length > 1000
            ? stderr.substring(0, 1000) + '...(truncated)'
            : stderr;
        console.error(
          `[APK Repacker] Command error output: ${truncatedStderr}`
        );
      }

      // Clean up the unique temporary directory
      try {
        if (fs.existsSync(uniqueTmpDir)) {
          fs.rmSync(uniqueTmpDir, { recursive: true, force: true });
          console.log(
            `[APK Repacker] Cleaned up unique temporary directory: ${uniqueTmpDir}`
          );
        }
      } catch (cleanupErr) {
        console.error(
          `[APK Repacker] Failed to clean up unique temporary directory: ${cleanupErr instanceof Error ? cleanupErr.message : String(cleanupErr)}`
        );
      }

      // Check if the output APK was created
      if (!fs.existsSync(outputApkPath)) {
        console.error('[APK Repacker] Failed to create repacked APK');
        return {
          success: false,
          error: 'Failed to create repacked APK',
          details: { stdout, stderr },
        };
      }

      console.log(
        `[APK Repacker] Successfully created repacked APK at: ${outputApkPath}`
      );

      // Calculate elapsed time in seconds
      const endTime = Date.now();
      const timeInSeconds = ((endTime - startTime) / 1000).toFixed(2);
      console.log(
        `[APK Repacker] Repacking completed in ${timeInSeconds} seconds`
      );

      // Success - return the job ID, APK name, and timing information
      return {
        success: true,
        message: 'APK repacked successfully',
        jobId,
        apkName: outputApkName,
        timeInSeconds: parseFloat(timeInSeconds),
      };
    } catch (err) {
      // Clean up the unique temporary directory in case of error
      try {
        if (fs.existsSync(uniqueTmpDir)) {
          fs.rmSync(uniqueTmpDir, { recursive: true, force: true });
          console.log(
            `[APK Repacker] Cleaned up unique temporary directory after error: ${uniqueTmpDir}`
          );
        }
      } catch (cleanupErr) {
        console.error(
          `[APK Repacker] Failed to clean up unique temporary directory after error: ${cleanupErr instanceof Error ? cleanupErr.message : String(cleanupErr)}`
        );
      }

      console.error('Error executing repack script:', err);
      // Extract error details
      let errorMessage = 'Error executing repack script';
      let errorDetailsObj = {
        message: 'Unknown error occurred',
      };

      if (err instanceof Error) {
        errorDetailsObj.message = err.message || 'No error message available';

        // Check if stdout/stderr are available (from child_process errors)
        if ('stdout' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.stdout = err.stdout || '';
        }

        if ('stderr' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.stderr = err.stderr || '';
        }

        // For child_process errors, also include the command and code
        if ('cmd' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.cmd = err.cmd;
        }

        if ('code' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.code = err.code;
        }
      } else {
        errorDetailsObj.message = String(err);
      }

      console.error(
        '[APK Repacker] Error details:',
        JSON.stringify(errorDetailsObj, null, 2)
      );

      return {
        success: false,
        error: errorMessage,
        details: errorDetailsObj.message,
      };
    }
  } catch (err) {
    console.error('Error repacking APK:', err);
    const errorMessage = 'Error repacking APK';
    const errorDetails = err instanceof Error ? err.message : String(err);

    return {
      success: false,
      error: errorMessage,
      details: errorDetails || 'Unknown error occurred',
    };
  }
}

// Export the service functions
export const ApkRepackerService = {
  TMP_DIR,
  cleanupTempFiles,
  cleanupCompletedJobs,
  forceCleanupAllExceptRecent,
  validateApkFile,
  createRepackingJob,
};

export default ApkRepackerService;
