import crypto from 'crypto';
import { env } from '$env/dynamic/private';

// CSRF token configuration
const CSRF_SECRET = env.CSRF_SECRET;
const CSRF_EXPIRY = 60 * 60; // 1 hour in seconds

/**
 * Generate a CSRF token for a session
 * @param {string} sessionId - Session ID to bind the token to
 * @returns {string} - CSRF token
 */
export function generateCsrfToken(sessionId) {
  if (!sessionId) return '';

  // Create a timestamp for expiration
  const expires = Date.now() + CSRF_EXPIRY * 1000;

  // Create the token payload
  const payload = `${sessionId}:${expires}`;

  // Sign the payload
  const hmac = crypto.createHmac('sha256', CSRF_SECRET);
  hmac.update(payload);
  const signature = hmac.digest('hex');

  // Return the complete token
  return `${payload}:${signature}`;
}

/**
 * Verify a CSRF token
 * @param {string} token - CSRF token to verify
 * @param {string} sessionId - Session ID the token should be bound to
 * @returns {boolean} - Whether the token is valid
 */
export function verifyCsrfToken(token, sessionId) {
  if (!token || !sessionId) {
    return false;
  }

  // Handle FormDataEntryValue type
  const tokenStr = typeof token === 'string' ? token : String(token);

  // Split the token into its parts
  const parts = tokenStr.split(':');
  if (parts.length !== 3) {
    return false;
  }

  const [tokenSessionId, expiresStr, signature] = parts;

  // Check if the token is for the correct session
  if (tokenSessionId !== sessionId) {
    return false;
  }

  // Check if the token has expired
  const expires = parseInt(expiresStr, 10);
  if (isNaN(expires) || expires < Date.now()) {
    return false;
  }

  // Verify the signature
  const payload = `${tokenSessionId}:${expiresStr}`;
  const hmac = crypto.createHmac('sha256', CSRF_SECRET);
  hmac.update(payload);
  const expectedSignature = hmac.digest('hex');

  return signature === expectedSignature;
}
