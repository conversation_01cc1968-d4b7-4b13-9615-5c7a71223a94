<script>
  import { browser } from '$app/environment';
  import toastStore from '../stores/toastStore';
  
  // Subscribe to the toast store
  let toasts = [];
  toastStore.subscribe(value => {
    toasts = value;
  });
  
  // Function to remove a toast
  function removeToast(id) {
    toastStore.remove(id);
  }
</script>

{#if browser}
  <div class="toast-container">
    {#each toasts as toast (toast.id)}
      <div 
        class="toast-message {toast.type === 'error' ? 'toast-error' : ''}" 
        class:visible={true} 
        data-timestamp={toast.timestamp}
        on:click={() => removeToast(toast.id)}
      >
        {toast.message}
      </div>
    {/each}
  </div>
{/if}

<!-- No need to add styles here as they are already in admin.css -->
