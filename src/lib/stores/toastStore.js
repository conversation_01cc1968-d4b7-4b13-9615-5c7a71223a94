import { writable } from 'svelte/store';

/**
 
 */

// Create a writable store with initial empty array
const toasts = writable([]);

// Helper to generate unique ID
const generateId = () => Math.random().toString(36).substring(2, 9);

/**
 * Add a toast message to the store
 * @param {string} message - Message to display
 
 */
function addToast(message, type = 'success') {
  const id = generateId();
  const timestamp = Date.now();

  toasts.update((all) => [...all, { id, message, type, timestamp }]);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    removeToast(id);
  }, 5000);

  return id;
}

/**
 * Remove a toast from the store by ID
 
 */
function removeToast(id) {
  toasts.update((all) => all.filter((t) => t.id !== id));
}

/**
 * Clear all toasts from the store
 */
function clearToasts() {
  toasts.set([]);
}

export default {
  subscribe: toasts.subscribe,
  add: addToast,
  remove: removeToast,
  clear: clearToasts,
};
