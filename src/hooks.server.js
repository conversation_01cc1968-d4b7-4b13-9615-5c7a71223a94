import { minify } from 'html-minifier';

const minifyOpts = {
  collapseBooleanAttributes: true,
  collapseWhitespace: true,
  conservativeCollapse: true,
  decodeEntities: true,
  html5: true,
  ignoreCustomComments: [/^#/],
  minifyCSS: true,
  minifyJS: false,
  removeAttributeQuotes: true,
  removeComments: false,
  removeOptionalTags: true,
  removeRedundantAttributes: true,
  removeScriptTypeAttributes: true,
  removeStyleLinkTypeAttributes: true,
  sortAttributes: true,
  sortClassName: true,
};

const NOT_FOUND_RESPONSE = new Response('', { status: 404 });

export const handleError = () => {
  return NOT_FOUND_RESPONSE;
};

export const handle = async ({ event, resolve }) => {
  try {
    return await resolve(event, {
      transformPageChunk: ({ html, done }) => {
        return minify(html, minifyOpts);
      },
    });
  } catch (e) {
    return NOT_FOUND_RESPONSE;
  }
};
