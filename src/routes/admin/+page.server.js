import { redirect, fail } from '@sveltejs/kit';
import {
  createSession,
  getSession,
  signSession,
  verifySession,
  deleteSession,
} from '$lib/server/session';
import { verifyCsrfToken } from '$lib/server/csrf';
import { env } from '$env/dynamic/private';

// Admin authentication
const ADMIN_USERNAME = env.ADMIN_USERNAME || 'admin';
const ADMIN_PASSWORD = env.ADMIN_PASSWORD;

/** @type {import('./$types').PageServerLoad} */
export async function load({ cookies }) {
  // Skip authentication in development mode - redirect directly to dashboard
  if (env.NODE_ENV === 'development') {
    throw redirect(303, '/admin/overview');
  }

  // Get the signed session from the cookie
  const signedSession = cookies.get('admin_session');

  // Verify the session signature and get the session ID
  /** @type {string|null} */
  const sessionId = verifySession(signedSession);

  // Get the session data if the session ID is valid
  const session = sessionId ? getSession(sessionId) : null;

  // Check if user is authenticated
  const isAuthenticated = !!session;

  // If not authenticated, show login form
  if (!isAuthenticated) {
    return {
      authenticated: false,
    };
  }

  // If authenticated and on the main admin page, redirect to dashboard
  if (isAuthenticated) {
    throw redirect(303, '/admin/overview');
  }

  return {
    authenticated: isAuthenticated,
  };
}

/** @type {import('./$types').Actions} */
export const actions = {
  // Login action
  login: async ({ request, cookies }) => {
    const data = await request.formData();
    const username = data.get('username');
    const password = data.get('password');

    if (username === ADMIN_USERNAME && password === ADMIN_PASSWORD) {
      // Create a new session with user data
      const sessionData = {
        username,
        role: 'admin',
        loginTime: new Date().toISOString(),
      };

      // Generate a session ID and store the session
      const sessionId = createSession(sessionData);

      // Sign the session ID for security
      const signedSession = signSession(sessionId);

      // Set the session cookie
      cookies.set('admin_session', signedSession, {
        path: '/',
        httpOnly: true,
        sameSite: 'strict',
        secure: env.NODE_ENV === 'production',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      });

      // Redirect to dashboard after login
      throw redirect(303, '/admin/overview');
    }

    return { success: false, error: 'Invalid credentials' };
  },

  // Logout action
  logout: async ({ request, cookies }) => {
    // Get form data for CSRF token
    const data = await request.formData();
    const csrfToken = data.get('csrfToken');

    // Get the current session
    const signedSession = cookies.get('admin_session');
    /** @type {string|null} */
    const sessionId = verifySession(signedSession);

    // Verify CSRF token if session exists
    if (sessionId && csrfToken) {
      const tokenStr =
        typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (!verifyCsrfToken(tokenStr, sessionId)) {
        return { success: false, error: 'Invalid request token' };
      }
    }

    // Delete the session if it exists
    if (sessionId) {
      deleteSession(sessionId);
    }

    // Remove the session cookie
    cookies.delete('admin_session', { path: '/' });

    // Redirect to login page
    throw redirect(303, '/admin?login=true');
  },
};
