<script>
  // This layout wraps all admin pages
  import { page } from '$app/stores'; // Using page store for navigation
  import { enhance } from '$app/forms';
  import Toast from '$lib/components/Toast.svelte';
  import toastStore from '$lib/stores/toastStore';
  import { afterNavigate } from '$app/navigation';

  // Get the data from the server load function
  export let data;

  /**
   * @type {any}
   */
  export let form = undefined;

  // Define tabs
  const tabs = [
    { id: 'overview', label: 'Overview', path: '/admin/overview' },
    { id: 'teams', label: 'Teams', path: '/admin/teams' },
    { id: 'devices', label: 'Devices', path: '/admin/devices' },
    { id: 'clients', label: 'VPN', path: '/admin/clients' },
    { id: 'apk/template', label: 'APK', path: '/admin/apk/template' },
    { id: 'notifications', label: 'FCM', path: '/admin/notifications' },
  ];

  // Get current path to determine active tab
  $: currentPath = $page.url.pathname;
  $: activeTab = tabs.find(tab => currentPath.includes(tab.id)) || tabs[0];

  // Track previous form data to avoid duplicate toasts
  let prevFormMessage = '';
  let prevFormError = '';

  // Handle form data changes to show toast messages
  $: if (form?.message && form.message !== prevFormMessage) {
    toastStore.add(form.message, 'success');
    prevFormMessage = form.message;
  }
  $: if (form?.error && form.error !== prevFormError) {
    toastStore.add(form.error, 'error');
    prevFormError = form.error;
  }

  // Clear toasts and reset previous form values when navigating between pages
  afterNavigate(() => {
    toastStore.clear();
    prevFormMessage = '';
    prevFormError = '';
  });
</script>

{#if !data.authenticated}
  <!-- Login form is handled in the main admin page, not in the layout -->
  <slot />
{:else}
  <!-- Admin Dashboard with Tabs -->
  <div class="admin-container">
    <header class="admin-header">
      <div class="admin-title-container">
        <img src="/favicon.webp" alt="Logo" class="admin-logo">
        <h1 class="admin-title">Admin Dashboard</h1>
      </div>

      <div class="admin-actions">
        <form method="POST" action="/admin?/logout" use:enhance>
          {#if data.csrfToken}
            <input type="hidden" name="csrfToken" value={data.csrfToken}>
          {/if}
          <button type="submit" class="button button-small">Logout</button>
        </form>
      </div>
    </header>

    <!-- Tab Navigation -->
    <div class="admin-tabs">
      {#each tabs as tab}
        <a
          href={tab.path}
          class="admin-tab {currentPath.includes(tab.id) ? 'active' : ''}"
          data-sveltekit-reload
        >
          {tab.label}
        </a>
      {/each}
    </div>

    <!-- Tab Content -->
    <div class="admin-tab-content">
      <slot />
    </div>
  </div>
{/if}

<!-- Centralized Toast Component -->
<Toast />
