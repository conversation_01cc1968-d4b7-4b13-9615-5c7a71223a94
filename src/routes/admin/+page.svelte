<script>
  import { enhance } from '$app/forms';

  let { data, form } = $props();
</script>

<svelte:head>
  <title>Admin Login</title>
</svelte:head>

{#if !data.authenticated}
  <!-- Login Form -->
  <div class="login-container">
    <form method="POST" action="?/login" use:enhance>
      <div class="form-group">
        <label for="username" class="form-label">Username</label>
        <input type="text" id="username" name="username" class="form-input" required>
      </div>

      <div class="form-group">
        <label for="password" class="form-label">Password</label>
        <input type="password" id="password" name="password" class="form-input" required>
      </div>

      {#if form?.error}
        <div class="form-error" role="alert">{form.error}</div>
      {/if}

      <button type="submit" class="button form-submit">Login</button>
    </form>
  </div>
{:else}
  <div class="loading-container">
    <p>Redirecting to dashboard...</p>
  </div>
{/if}