import { supabase } from '$lib/server/supabase';

/** @type {import('./$types').PageServerLoad} */
export async function load({ parent }) {
  // Get parent data which includes authentication status
  await parent(); // This ensures layout authentication check runs first

  try {
    // Calculate date ranges for analytics
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
    const last7Days = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Fetch comprehensive overview statistics from the database
    const [
      teamsResult,
      devicesResult,
      walletsResult,
      transactionsResult,
      recentTeamsResult,
      lastMonthTeamsResult,
      totalBalanceResult,
      recentTransactionsResult,
      last7DaysTransactionsResult,
      activeDevicesResult,
      teamsWithBalanceResult,
      lowBalanceTeamsResult,
      recentDevicesResult,
      topTeamsByBalanceResult,
      transactionTrendsResult
    ] = await Promise.all([
      // Core counts
      supabase.from('teams').select('*', { count: 'exact', head: true }),
      supabase.from('devices').select('*', { count: 'exact', head: true }),
      supabase.from('wallets').select('*', { count: 'exact', head: true }),
      supabase.from('transactions').select('amount, created_at'),

      // Growth metrics
      supabase.from('teams').select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString()),
      supabase.from('teams').select('*', { count: 'exact', head: true })
        .gte('created_at', startOfLastMonth.toISOString())
        .lt('created_at', startOfMonth.toISOString()),

      // Financial metrics
      supabase.from('teams').select('balance, created_at'),
      supabase.from('transactions').select('amount, created_at, description')
        .gte('created_at', last30Days.toISOString())
        .order('created_at', { ascending: false }),
      supabase.from('transactions').select('amount, created_at')
        .gte('created_at', last7Days.toISOString()),

      // Device analytics
      supabase.from('devices').select('last_auth_at, created_at, team_id')
        .not('last_auth_at', 'is', null)
        .gte('last_auth_at', last7Days.toISOString()),

      // Team health metrics
      supabase.from('teams').select('id, balance').gt('balance', 0),
      supabase.from('teams').select('id, balance').lt('balance', 10),
      supabase.from('devices').select('created_at, team_id')
        .gte('created_at', last7Days.toISOString()),

      // Top performers
      supabase.from('teams').select('id, balance')
        .order('balance', { ascending: false })
        .limit(5),

      // Transaction trends (last 30 days grouped)
      supabase.from('transactions').select('amount, created_at')
        .gte('created_at', last30Days.toISOString())
        .order('created_at', { ascending: true })
    ]);

    // Calculate comprehensive business metrics
    const totalTeams = teamsResult.count || 0;
    const totalDevices = devicesResult.count || 0;
    const totalWallets = walletsResult.count || 0;
    const totalTransactions = transactionsResult.data?.length || 0;
    const newTeamsThisMonth = recentTeamsResult.count || 0;
    const newTeamsLastMonth = lastMonthTeamsResult.count || 0;
    const activeDevices = activeDevicesResult.count || 0;
    const teamsWithBalance = teamsWithBalanceResult.count || 0;
    const lowBalanceTeams = lowBalanceTeamsResult.count || 0;
    const newDevicesThisWeek = recentDevicesResult.count || 0;

    // Financial calculations
    const totalBalance = totalBalanceResult.data?.reduce((sum, team) => {
      return sum + (parseFloat(team.balance) || 0);
    }, 0) || 0;

    const totalRevenue = transactionsResult.data?.reduce((sum, tx) => {
      const amount = parseFloat(tx.amount) || 0;
      return amount > 0 ? sum + amount : sum; // Only count positive transactions (deposits)
    }, 0) || 0;

    const monthlyRevenue = recentTransactionsResult.data?.reduce((sum, tx) => {
      const amount = parseFloat(tx.amount) || 0;
      return amount > 0 ? sum + amount : sum;
    }, 0) || 0;

    const weeklyRevenue = last7DaysTransactionsResult.data?.reduce((sum, tx) => {
      const amount = parseFloat(tx.amount) || 0;
      return amount > 0 ? sum + amount : sum;
    }, 0) || 0;

    // Business intelligence metrics
    const avgBalancePerTeam = totalTeams > 0 ? totalBalance / totalTeams : 0;
    const avgDevicesPerTeam = totalTeams > 0 ? totalDevices / totalTeams : 0;
    const avgRevenuePerTeam = totalTeams > 0 ? totalRevenue / totalTeams : 0;
    const teamGrowthRate = newTeamsLastMonth > 0 ? ((newTeamsThisMonth - newTeamsLastMonth) / newTeamsLastMonth) * 100 : 0;
    const activeTeamRate = totalTeams > 0 ? (teamsWithBalance / totalTeams) * 100 : 0;
    const deviceUtilizationRate = totalDevices > 0 ? (activeDevices / totalDevices) * 100 : 0;

    // Top teams by balance
    const topTeams = topTeamsByBalanceResult.data?.map(team => ({
      id: team.id,
      balance: parseFloat(team.balance) || 0
    })) || [];

    // Calculate daily revenue trend for last 7 days
    const dailyRevenueTrend = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const dayRevenue = last7DaysTransactionsResult.data?.reduce((sum, tx) => {
        const txDate = new Date(tx.created_at);
        const amount = parseFloat(tx.amount) || 0;
        if (txDate >= dayStart && txDate < dayEnd && amount > 0) {
          return sum + amount;
        }
        return sum;
      }, 0) || 0;

      dailyRevenueTrend.push({
        date: dayStart.toISOString().split('T')[0],
        revenue: dayRevenue
      });
    }

    return {
      stats: {
        // Core metrics
        totalTeams,
        totalDevices,
        totalWallets,
        totalTransactions,

        // Growth metrics
        newTeamsThisMonth,
        newTeamsLastMonth,
        teamGrowthRate,
        newDevicesThisWeek,

        // Financial metrics
        totalBalance,
        totalRevenue,
        monthlyRevenue,
        weeklyRevenue,
        avgRevenuePerTeam,

        // Operational metrics
        activeDevices,
        deviceUtilizationRate,
        avgDevicesPerTeam,
        avgBalancePerTeam,

        // Health metrics
        teamsWithBalance,
        lowBalanceTeams,
        activeTeamRate,

        // Analytics
        topTeams,
        dailyRevenueTrend
      }
    };
  } catch (error) {
    console.error('Error fetching overview data:', error);

    // Return comprehensive fallback data in case of error
    return {
      stats: {
        // Core metrics
        totalTeams: 0,
        totalDevices: 0,
        totalWallets: 0,
        totalTransactions: 0,

        // Growth metrics
        newTeamsThisMonth: 0,
        newTeamsLastMonth: 0,
        teamGrowthRate: 0,
        newDevicesThisWeek: 0,

        // Financial metrics
        totalBalance: 0,
        totalRevenue: 0,
        monthlyRevenue: 0,
        weeklyRevenue: 0,
        avgRevenuePerTeam: 0,

        // Operational metrics
        activeDevices: 0,
        deviceUtilizationRate: 0,
        avgDevicesPerTeam: 0,
        avgBalancePerTeam: 0,

        // Health metrics
        teamsWithBalance: 0,
        lowBalanceTeams: 0,
        activeTeamRate: 0,

        // Analytics
        topTeams: [],
        dailyRevenueTrend: []
      },
      error: 'Failed to load overview data'
    };
  }
}
