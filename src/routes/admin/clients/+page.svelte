<script>
  import { enhance } from '$app/forms';
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { invalidateAll } from '$app/navigation';

  // Get the data from the server load function
  export let data;
  export let form;

  // Local state
  let clients = data.clients || [];

  // Keep clients in sync with data.clients after refresh
  $: clients = data.clients || [];

  let loading = false;
  /**
   * @type {number | null | undefined}
   */
  let refreshInterval = null;
  /**
   * @type {number | null | undefined}
   */
  let countdownInterval = null;
  let lastUpdated = new Date();
  let nextRefreshIn = 5;

  // Stats calculations
  $: totalClients = clients.length;
  // @ts-ignore
  $: activeClients = clients.filter(client => client.status?.desired === "enabled").length;
  // @ts-ignore
  $: inactiveClients = clients.filter(client => client.status?.desired === "disabled").length;

  // This function is called after form actions to trigger reactivity
  // The layout will handle showing the toast based on the updated form data
  function showToastAgain() {
    // We don't need to do anything here anymore
    // The layout will detect the form change and show the toast
  }

  // Function to refresh data from server (no env exposed)
  async function refreshData() {
    if (loading || !data.authenticated) return;
    loading = true;
    try {
      await invalidateAll();
      lastUpdated = new Date();
      nextRefreshIn = 5;
    } catch (error) {
      // Handle error if needed
    } finally {
      loading = false;
    }
  }

  // Function to update countdown timer
  function updateCountdown() {
    if (nextRefreshIn > 0) {
      nextRefreshIn -= 1;
    }
  }

  // Use a manual initialization approach
  let initialized = false;

  // Function to initialize the component
  function initialize() {
    if (initialized) return;
    initialized = true;

    // Initial data load
    refreshData();

    // Set up refresh interval (every 5 seconds)
    if (browser) {
      refreshInterval = window.setInterval(refreshData, 5000);

      // Set up countdown interval (every 1 second)
      countdownInterval = window.setInterval(updateCountdown, 1000);
    }
  }

  // Function to clean up resources
  function cleanup() {
    if (refreshInterval) {
      window.clearInterval(refreshInterval);
      refreshInterval = null;
    }
    if (countdownInterval) {
      window.clearInterval(countdownInterval);
      countdownInterval = null;
    }
  }

  // Initialize on mount
  onMount(() => {
    initialize();

    // Cleanup when component is unmounted
    return cleanup;
  });

  // Also initialize when the component is first rendered
  if (browser) {
    initialize();
  }

  // Format date
  // @ts-ignore
  function formatDate(dateString) {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (error) {
      return 'Invalid date';
    }
  }

  // Format time ago
  // @ts-ignore
  function timeAgo(dateString) {
    try {
      const date = new Date(dateString);
      const now = new Date();
      // Get time difference in milliseconds and convert to seconds
      const diffMs = now.getTime() - date.getTime();
      const seconds = Math.floor(diffMs / 1000);

      let interval = Math.floor(seconds / 31536000);
      if (interval > 1) return interval + ' years ago';

      interval = Math.floor(seconds / 2592000);
      if (interval > 1) return interval + ' months ago';

      interval = Math.floor(seconds / 86400);
      if (interval > 1) return interval + ' days ago';

      interval = Math.floor(seconds / 3600);
      if (interval > 1) return interval + ' hours ago';

      interval = Math.floor(seconds / 60);
      if (interval > 1) return interval + ' minutes ago';

      return Math.floor(seconds) + ' seconds ago';
    } catch (error) {
      return 'Unknown time';
    }
  }
</script>

<svelte:head>
  <title>Phantom - VPN Clients</title>
</svelte:head>

<!-- Clients Table -->
<div class="admin-card">
  <div class="admin-card-title">
    VPN Clients
  </div>

  <!-- Stats Cards -->
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total:</span>
        <span class="stat-value-compact">{totalClients}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Active:</span>
        <span class="stat-value-compact" style="color: #4caf50;">{activeClients}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Inactive:</span>
        <span class="stat-value-compact" style="color: #f44336;">{inactiveClients}</span>
      </div>
    </div>
  </div>

  {#if clients.length === 0}
    <div class="empty-state">
      <p>Clients will appear here when VPN nodes will connect.</p>
    </div>
  {:else}
    <table class="client-table">
      <thead>
        <tr>
          <th>Actions</th>
          <th>ID</th>
          <th>Status</th>
          <th>Sync</th>
          <th>IPv4</th>
          <th>Node</th>
          <th>Last Update</th>
        </tr>
      </thead>
      <tbody>
        {#each clients as client}
          <tr>
            <td>
              <form method="POST" action="?/toggleClient" use:enhance={() => {
                return async ({ result, update }) => {
                  // Update the form result
                  await update();

                  // If the toggle was successful, refresh the client data
                  if (result.type === 'success') {
                    await refreshData();
                    // Show toast message again
                    showToastAgain();
                  }
                };
              }}>
                <input type="hidden" name="clientId" value={client.id}>
                <input type="hidden" name="enabled" value={client.status?.desired === 'enabled'}>
                {#if data.csrfToken}
                  <input type="hidden" name="csrfToken" value={data.csrfToken}>
                {/if}
                <button
                  type="submit"
                  class={`button button-small client-toggle-btn ${client.status?.desired === 'enabled' ? 'client-btn-off' : 'client-btn-on'}`}
                  style={`background: ${client.status?.desired === 'enabled' ? 'linear-gradient(to right, #f44336, #ff9800)' : 'linear-gradient(to right, #4caf50, #8bc34a)'} !important;
                         padding: 0.2rem 0.4rem !important;
                         font-size: 0.7rem !important;
                         min-width: 60px !important;
                         border-radius: 4px !important;`}
                >
                  {client.status?.desired === 'enabled' ? 'Turn Off' : 'Turn On'}
                </button>
              </form>
            </td>
            <td class="id-cell"><span class="client-id">{client.id}</span></td>
            <td class="status-cell">
              <div class="status-combined">
                <span class={client.status?.desired === 'enabled' ? 'status-enabled' : 'status-disabled'}>
                  D: {client.status?.desired === 'enabled' ? 'On' : 'Off'}
                </span>
                <span class="status-separator">/</span>
                <span class={client.status?.actual === 'enabled' ? 'status-enabled' : 'status-disabled'}>
                  A: {client.status?.actual === 'enabled' ? 'On' : 'Off'}
                </span>
              </div>
            </td>
            <td class="sync-cell">
              <span class={`sync-status sync-${client.status?.syncStatus || 'unknown'}`}>
                {client.status?.syncStatus === 'synced' ? 'Synced' :
                 client.status?.syncStatus === 'pending' ? 'Pending' :
                 client.status?.syncStatus === 'conflict' ? 'Conflict' : 'Unknown'}
              </span>

              {#if client.status?.syncStatus === 'conflict'}
                <form method="POST" action="?/trustNodeState" use:enhance={() => {
                  return async ({ result, update }) => {
                    // Update the form result
                    await update();

                    // If the action was successful, refresh the client data
                    if (result.type === 'success') {
                      await refreshData();
                      // Show toast message again
                      showToastAgain();
                    }
                  };
                }}>
                  <input type="hidden" name="clientId" value={client.id}>
                  {#if data.csrfToken}
                    <input type="hidden" name="csrfToken" value={data.csrfToken}>
                  {/if}
                  <button
                    type="submit"
                    class="button button-small client-trust-btn"
                    style="background: linear-gradient(to right, #333, #555) !important;
                           padding: 0.2rem 0.4rem !important;
                           font-size: 0.7rem !important;
                           min-width: 60px !important;
                           border-radius: 4px !important;"
                  >
                    Trust Node
                  </button>
                </form>
              {/if}
            </td>
            <td>
              {#if client.ips && client.ips.length > 0}
                {@const ipv4Addresses = client.ips.filter(
                  // @ts-ignore
                  (ip) => ip && ip.type === 'ipv4'
                )}
                <div style="max-width: 180px; overflow-wrap: break-word;">
                  {#if ipv4Addresses.length > 0}
                    {#each ipv4Addresses as ip, i (ip.ip)}
                      {ip.ip.replace('/32', '')}{#if i < ipv4Addresses.length - 1}, {/if}
                    {/each}
                  {:else}
                    -
                  {/if}
                </div>
              {:else}
                -
              {/if}
            </td>
            <td>{client.nodeId || '-'}</td>
            <td title={formatDate(client.lastUpdate)}>
              {timeAgo(client.lastUpdate)}
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}

  <div class="timestamp">
    Last updated: {formatDate(lastUpdated)}
    <span class="refresh-countdown">(Next refresh in {nextRefreshIn}s)</span>
  </div>
</div>
