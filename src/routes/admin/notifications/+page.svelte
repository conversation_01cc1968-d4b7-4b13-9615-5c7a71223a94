<script>
    import { enhance } from '$app/forms';
    import toastStore from '$lib/stores/toastStore';

    export let data;

    let loading = false;
    let title = '';
    let message = '';
    let selectedDevice = '';

    function handleSubmit() {
        loading = true;
    }

    function handleResult(result) {
        loading = false;
        result = result?.data;

        if (result?.success) {
            // Clear form on success
            title = '';
            message = '';
            selectedDevice = '';

            // Show success toast with details
            let successMessage = 'Notification sent successfully!';
            if (result.sentTo) {
                successMessage += ` Sent to: ${result.sentTo}`;
            }
            if (result.response?.successful) {
                successMessage += ` (${result.response.successful} device(s))`;
            }
            toastStore.add(successMessage, 'success');

        } else if (result?.error) {
            toastStore.add(result.error, 'error');
        } else if (result?.type === 'error' && result?.status) {
            toastStore.add(`Server error (${result.status})`, 'error');
        } else {
            toastStore.add('Unknown error occurred', 'error');
        }
    }
</script>

<div class="admin-card">
    <div class="admin-card-header">
        <div class="admin-card-title">FCM Notifications</div>
    </div>



    {#if data.error}
        <div class="message error">
            <strong>Error loading devices:</strong> {data.error}
        </div>
    {/if}

    <form
        method="POST"
        action="?/sendNotification"
        use:enhance={() => {
            handleSubmit();
            return async ({ result }) => handleResult(result);
        }}
    >
        <div class="form-group">
            <label for="title" class="form-label">Title:</label>
            <input
                type="text"
                id="title"
                name="title"
                bind:value={title}
                required
                placeholder="Notification title"

                class="form-input"
            />
        </div>

        <div class="form-group">
            <label for="message" class="form-label">Message:</label>
            <textarea
                id="message"
                name="message"
                bind:value={message}
                required
                placeholder="Notification message"

                class="form-input"
            ></textarea>
        </div>

        <div class="form-group">
            <label for="selectedDevice" class="form-label">Send to device:</label>
            <select
                id="selectedDevice"
                name="selectedDevice"
                bind:value={selectedDevice}
                required

                class="form-input"
            >
                <option value="">Select a device...</option>
                {#each data.devices as device}
                    <option value={device.internal_id}>
                        📱 {device.display_name} {device.has_fcm_token ? '✅' : '❌'}
                    </option>
                {/each}
            </select>
            <small>
                {#if selectedDevice}
                    Will send to the specific device (team assigned automatically)
                {:else}
                    Choose a device to send notification to
                {/if}
            </small>
        </div>



        <button type="submit" disabled={loading}>
            {loading ? 'Sending...' : 'Send Notification'}
        </button>
    </form>


</div>
