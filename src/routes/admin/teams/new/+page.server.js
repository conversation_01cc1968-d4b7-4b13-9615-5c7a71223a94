import { fail, redirect } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';
import { v4 as uuidv4 } from 'uuid';
import { generateTeamWallets } from '$lib/server/walletService.js';

// SvelteKit page server for /team/new (no server data needed)
export const load = async () => {
  return {};
};

export const actions = {
  default: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');
    const balance = formData.get('balance');

    // Validate required fields
    if (!id || typeof balance === 'undefined' || balance === null) {
      return fail(400, {
        success: false,
        error: 'Missing required fields: id, balance',
      });
    }

    // Auto-generate internal_id and created_at if not provided
    const now = new Date().toISOString();
    const nextChargeDate = new Date(now);
    nextChargeDate.setDate(nextChargeDate.getDate() + 30);
    const nextChargeAt = nextChargeDate.toISOString();
    const team = {
      internal_id: formData.get('internal_id') || uuidv4(),
      id: id.toString(), // Ensure id is a string
      created_at: formData.get('created_at') || now,
      next_charge_at: nextChargeAt,
      balance: parseFloat(balance.toString()), // Ensure balance is a number
      owner_internal_id: formData.get('owner_internal_id') || null,
      owner_id: formData.get('owner_id') || null,
    };

    const { data, error: dbError } = await supabase
      .from('teams')
      .insert([team])
      .select()
      .single();

    if (dbError) {
      return fail(400, { success: false, error: dbError.message });
    }

    // Generate wallets for the new team
    try {
      console.log(`Generating wallets for new team: ${data.id}`);
      await generateTeamWallets(data.internal_id, data.id);
      console.log(`Successfully generated wallets for team: ${data.id}`);
    } catch (walletError) {
      console.error(
        `Failed to generate wallets for team ${data.id}:`,
        walletError
      );
      // Don't fail the team creation if wallet generation fails
      // The team is created successfully, wallets can be generated later
    }

    throw redirect(303, `/admin/teams/`);
    //throw redirect(303, `/admin/teams/${data.id}`);
  },
};
