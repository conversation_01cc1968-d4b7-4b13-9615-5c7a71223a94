import { error, redirect } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';

export const load = async ({ params }) => {
  const id = params.slug;
  const { data, error: dbError } = await supabase
    .from('devices')
    .select(
      `
          *,
          teams!devices_team_id_fkey (
            id,
            balance
          )
        `
    )
    .eq('internal_id', id)
    .single();

  if (dbError) {
    console.error('Error fetching device:', dbError);
    throw error(404, 'Device not found');
  }

  return { device: data };
};

export const actions = {
  delete: async ({ params }) => {
    const id = params.slug;
    const { error: dbError } = await supabase
      .from('devices')
      .delete()
      .eq('internal_id', id);

    if (dbError) {
      throw error(500, dbError.message || 'Failed to delete device');
    }

    throw redirect(303, '/admin/devices');
  },

  update: async ({ request, params }) => {
    const currentId = params.slug;
    const formData = await request.formData();

    // Define the shape of our update fields with proper types
    /** @type {Record<string, string | null>} */
    const updateFields = {
      team_id: formData.get('team_id')?.toString() || null,
      ip: formData.get('ip')?.toString() || null,
      nickname: formData.get('nickname')?.toString() || null,
      role: formData.get('role')?.toString() || null,
      vpn_conf: formData.get('vpn_conf')?.toString() || null,
      msg_conf: formData.get('msg_conf')?.toString() || null,
      phone_conf: formData.get('phone_conf')?.toString() || null,
    };

    // Remove empty strings and convert to null
    for (const key in updateFields) {
      if (updateFields[key] === '') {
        updateFields[key] = null;
      }
    }

    // If no fields to update, return early
    if (Object.values(updateFields).every((val) => val === null)) {
      return { success: true };
    }

    // If team_id is provided, verify it exists and get internal_id
    if (updateFields.team_id) {
      const { data: team, error: teamError } = await supabase
        .from('teams')
        .select('internal_id')
        .eq('id', updateFields.team_id)
        .single();

      if (teamError || !team) {
        return {
          success: false,
          error: 'team not found',
        };
      }

      // Add team_internal_id to the update fields
      updateFields.team_internal_id = team.internal_id;
    }

    try {
      // First, update the device
      const { data: updatedDevice, error: updateError } = await supabase
        .from('devices')
        .update(updateFields)
        .eq('internal_id', currentId)
        .select(
          `
                    *,
                    teams!devices_team_id_fkey (
                        id,
                        balance
                    )
                `
        )
        .single();

      if (updateError) throw updateError;
      if (!updatedDevice) throw new Error('No data returned from update');

      // Redirect to devices list after successful update
      throw redirect(303, '/admin/devices');
    } catch (/** @type {any} */ err) {
      console.error('Error updating device:', err);
      if (err.status === 303) throw err; // Re-throw redirect
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to update device',
      };
    }
  },
};
