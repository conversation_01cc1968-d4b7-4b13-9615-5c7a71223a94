<script lang="ts">
  import type { PageData } from "./$types";
  import { enhance } from "$app/forms";
  import { goto } from "$app/navigation";

  export let data: PageData;

  // Define the expected shape of the device data
  type Device = {
    internal_id: string;
    team_id: string;
    team_internal_id: string;
    ip?: string | null;
    nickname?: string | null;
    vpn_conf?: string | null;
    msg_conf?: string | null;
    phone_conf?: string | null;
    role?: string | null;
    created_at: string;
    last_auth_at?: string | null;
    teams?: {
      id: string;
      balance: number;
    };
  };

  // Type assertion for data.device
  let device: Device | null = data.device ? (data.device as Device) : null;

  // Form fields
  let ip: string | undefined = device?.ip || "";
  let nickname: string | undefined = device?.nickname || "";
  let vpnConf: string | undefined = device?.vpn_conf || "";
  let msgConf: string | undefined = device?.msg_conf || "";
  let phoneConf: string | undefined = device?.phone_conf || "";
  let teamId: string | undefined = device?.team_id || "";
  let role: string | undefined = device?.role || "Single";

  let successMessage: string | null = null;
  let errorMessage: string | null = null;
  let isSubmitting = false;

  // Update form fields when device data changes
  $: if (device) {
    ip = device.ip || "";
    nickname = device.nickname || "";
    vpnConf = device.vpn_conf || "";
    msgConf = device.msg_conf || "";
    phoneConf = device.phone_conf || "";
    teamId = device.team_id || "";
    role = device.role || "Single";
    successMessage = null;
    errorMessage = null;
  }

  // Format date for display
  function formatDate(dateString: string | undefined) {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return date.toLocaleString(undefined, {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Invalid date";
    }
  }

  // Format time ago
  function timeAgo(dateString: string | undefined) {
    if (!dateString) return "Never";
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const seconds = Math.floor(diffMs / 1000);

      const intervals = [
        { label: "year", seconds: 31536000 },
        { label: "month", seconds: 2592000 },
        { label: "day", seconds: 86400 },
        { label: "hour", seconds: 3600 },
        { label: "minute", seconds: 60 },
        { label: "second", seconds: 1 },
      ];

      for (const interval of intervals) {
        const count = Math.floor(seconds / interval.seconds);
        if (count >= 1) {
          return count === 1
            ? `1 ${interval.label} ago`
            : `${count} ${interval.label}s ago`;
        }
      }

      return "Just now";
    } catch {
      return "Unknown time";
    }
  }

  async function handleSubmit(e: SubmitEvent) {
    e.preventDefault();
    isSubmitting = true;
    errorMessage = null;
    successMessage = null;

    // Ensure the event target is a form element
    if (!(e.target instanceof HTMLFormElement)) {
      errorMessage = "Invalid form element";
      isSubmitting = false;
      return;
    }

    const form = e.target;

    try {
      const formData = new FormData(form);

      const response = await fetch(`?/update`, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        successMessage = "Device updated successfully";
        // Update the local device data
        if (device) {
          device = { ...device, ...result.device };
        }
      } else {
        errorMessage = result.error || "Failed to update device";
      }
    } catch (err) {
      console.error("Error updating device:", err);
      errorMessage = "An error occurred while updating the device";
    } finally {
      isSubmitting = false;
    }
  }
</script>

<div class="admin-container">
  {#if device}
    <div class="admin-card">
      <h1 class="admin-title">{device.ip} - Device Details</h1>

      <hr
        style="margin-top: 0.5rem; margin-bottom: 1rem; border-color: #333;"
      />

      {#if successMessage}
        <div class="success-message">
          <span>{successMessage}</span>
        </div>
      {/if}

      {#if errorMessage}
        <div class="error-message">
          <span>{errorMessage}</span>
        </div>
      {/if}

      <div class="team-info-grid">
        <div class="team-info-item">
          <strong>Device IP:</strong>
          {device.ip || "N/A"}
          {#if device.internal_id}
            <div style="font-size: 0.8em; margin-top: 0.2em;">
              <strong>Internal ID:</strong>
              {device.internal_id}
            </div>
          {/if}
        </div>

        <div class="team-info-item">
          <strong>team ID:</strong>
          {device.team_id || "N/A"}
          {#if device.team_internal_id}
            <div style="font-size: 0.8em; margin-top: 0.2em;">
              <strong>team Internal ID:</strong>
              {device.team_internal_id}
            </div>
          {/if}
        </div>

        <div class="team-info-item date-group">
          <div>
            <strong>Created At:</strong>
            {formatDate(device.created_at)}
          </div>
          {#if device.last_auth_at}
            <div>
              <strong>Last Active:</strong>
              {timeAgo(device.last_auth_at)}
            </div>
          {/if}
        </div>
      </div>

      <hr
        style="margin-top: 0.5rem; margin-bottom: 1rem; border-color: #333;"
      />

      <div class="form-container">
        <form
          method="POST"
          action="?/update"
          use:enhance
          on:submit|preventDefault={handleSubmit}
          class="form-section"
        >
          <div class="form-grid">
            <div class="form-group">
              <label for="team_id">team ID</label>
              <div class="input-container">
                <input
                  type="text"
                  id="team_id"
                  name="team_id"
                  bind:value={teamId}
                  class="form-input"
                  required
                />
              </div>
            </div>

            <div class="form-group">
              <label for="ip">IP Address</label>
              <div class="input-container">
                <input
                  type="text"
                  id="ip"
                  name="ip"
                  bind:value={ip}
                  class="form-input"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="nickname">Nickname</label>
              <div class="input-container">
                <input
                  type="text"
                  id="nickname"
                  name="nickname"
                  bind:value={nickname}
                  class="form-input"
                  placeholder="Optional display name"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="role" class="form-label">Role</label>
              <select
                id="role"
                name="role"
                bind:value={role}
                class="form-input"
              >
                <option value="" disabled={!!role}>Select Role</option>
                <option value="Single">Single</option>
                <option value="TeamLead">Team Lead</option>
                <option value="TeamMember">Team Member</option>
              </select>
            </div>

            <div class="form-group full-width">
              <div class="config-textarea">
                <label for="vpn_conf"> VPN Configuration </label>
                <div class="input-container">
                  <textarea
                    id="vpn_conf"
                    name="vpn_conf"
                    bind:value={vpnConf}
                    rows={6}
                    class="form-textarea config-textarea"
                    placeholder="Paste VPN configuration here..."
                    spellcheck="false"
                  ></textarea>
                </div>
              </div>
            </div>

            <div class="form-group full-width">
              <div class="config-textarea">
                <label for="msg_conf"> Message Configuration </label>
                <div class="input-container">
                  <textarea
                    id="msg_conf"
                    name="msg_conf"
                    bind:value={msgConf}
                    rows={6}
                    class="form-textarea config-textarea"
                    placeholder="Paste message configuration here..."
                    spellcheck="false"
                  ></textarea>
                </div>
              </div>
            </div>

            <div class="form-group full-width">
              <div class="config-textarea">
                <label for="phone_conf"> Phone Configuration </label>
                <div class="input-container">
                  <textarea
                    id="phone_conf"
                    name="phone_conf"
                    bind:value={phoneConf}
                    rows={6}
                    class="form-textarea config-textarea"
                    placeholder="Paste phone configuration here..."
                    spellcheck="false"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button
              class="button button-secondary"
              type="button"
              on:click={() => goto("/admin/devices")}
            >
              Cancel
            </button>
            <button
              type="submit"
              class="button button-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      </div>
    </div>
  {:else}
    <div class="text-center py-12">
      <p class="text-gray-500">Device not found</p>
    </div>
  {/if}
</div>
