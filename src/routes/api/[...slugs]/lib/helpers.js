// src/routes/api/[...slugs]/lib/helpers.js

// ---- Utility Functions ----

/**
 * Get the client IP address, handling various proxy headers and localhost
 * @param {Request} request - The HTTP request
 * @returns {string} The client IP address
 */
export const getClientIP = (request) => {
  const headers = request.headers;

  // Try common proxy headers first
  const forwardedFor = headers.get('x-forwarded-for');
  if (forwardedFor) {
    // Get the first IP in the list
    return forwardedFor.split(',')[0].trim();
  }

  const realIP = headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  // Get the remote address from the connection
  // In a development environment where the client is localhost
  // this might be something like ::1 or 127.0.0.1
  const remoteAddr = request.headers.get('host')?.split(':')[0] || 'localhost';

  return remoteAddr;
};

// Authentication middleware
import { env } from '$env/dynamic/private';
const API_KEY = env.API_KEY;

export const authenticate = (context) => {
  // Skip authentication in development mode
  if (env.NODE_ENV === 'development') {
    return true;
  }

  const authHeader = context.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid Authorization header');
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  if (token !== API_KEY) {
    throw new Error('Invalid API key');
  }

  return true;
};
