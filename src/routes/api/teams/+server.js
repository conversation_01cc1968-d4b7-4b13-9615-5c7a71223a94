import { json } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';

/** @type {import('./$types').RequestHandler} */
export async function GET() {
  try {
    const { data: teams, error } = await supabase
      .from('teams')
      .select('id, balance, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching teams:', error);
      return json({ error: 'Failed to fetch teams' }, { status: 500 });
    }

    return json({
      success: true,
      teams: teams || [],
    });
  } catch (error) {
    console.error('Error in teams API:', error);
    return json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
