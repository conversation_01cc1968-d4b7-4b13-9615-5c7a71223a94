// IPN (Instant Payment Notification) webhook endpoint for WestWallet
import { json } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase.js';
import { sendNotification } from '$lib/server/notifications.js';
import { convertToUSD } from '$lib/server/currencyService.js';

// WestWallet IPN source IP for security validation
const WESTWALLET_IPN_IP = '***********';

/**
 * Validate that the request comes from WestWallet's authorized IP
 * @param {Request} request - The incoming request
 * @returns {boolean} True if request is from authorized IP
 */
function validateIpnSource(request) {
  // Get client IP from various headers (considering proxies)
  const clientIp =
    request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    request.headers.get('x-client-ip');

  console.log('[IPN] Request from IP:', clientIp);

  // In development, we might want to skip IP validation
  if (process.env.NODE_ENV === 'development') {
    console.log('[IPN] Development mode - skipping IP validation');
    return true;
  }

  return clientIp === WESTWALLET_IPN_IP;
}

async function parseIpnData(request) {
  const contentType = request.headers.get('content-type');

  if (contentType?.includes('application/x-www-form-urlencoded')) {
    // Parse form-encoded data
    const formData = await request.formData();
    const data = {};
    for (const [key, value] of formData.entries()) {
      data[key] = value;
    }
    return data;
  } else if (contentType?.includes('application/json')) {
    // Parse JSON data (fallback)
    return await request.json();
  } else {
    throw new Error('Unsupported content type');
  }
}

/**
 * Extract team ID from wallet address label
 * @param {string} label - The wallet label (e.g., "team_ABC123_USDTTRC")
 * @returns {string|null} Team ID or null if not found
 */
function extractTeamIdFromLabel(label) {
  if (!label) return null;

  // Expected format: team_{teamId}_{currency}
  const match = label.match(/^team_([^_]+)_/);
  return match ? match[1] : null;
}

/**
 * Find wallet and team information by address
 * @param {string} address - Wallet address
 * @returns {Promise<Object|null>} Wallet and team data or null
 */
async function findWalletByAddress(address) {
  const { data, error } = await supabase
    .from('wallets')
    .select('*')
    .eq('address', address)
    .single();

  if (error) {
    console.error('[IPN] Error finding wallet by address:', error);
    return null;
  }

  return data;
}

export async function POST({ request }) {
  console.log('[IPN] Received payment notification from WestWallet');

  try {
    // Validate source IP
    if (!validateIpnSource(request)) {
      console.warn('[IPN] Unauthorized IP address');
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse IPN data
    const ipnData = await parseIpnData(request);
    console.log('[IPN] Parsed notification data:', ipnData);

    // Validate required fields
    const { id, amount, address, currency, status, blockchain_hash, label } =
      ipnData;
    const txInternalId = Number(id);
    if (Number.isNaN(txInternalId) || txInternalId <= 0) {
      console.error('[IPN] Invalid transaction id:', id);
      return json({ error: 'Invalid transaction id' }, { status: 400 });
    }

    // Only process notifications with status "completed"; ignore others like "pending"
    if (status !== 'completed') {
      console.log('[IPN] Ignoring notification with status:', status);
      return json({
        success: true,
        message: `Notification with status ${status} ignored`,
      });
    }

    if (!id || !amount || !address || !currency || !status) {
      console.error('[IPN] Missing required fields in notification');
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Find the wallet and associated team
    const walletData = await findWalletByAddress(address);
    console.log('[IPN] Found wallet data:', walletData);
    if (!walletData) {
      console.error('[IPN] Wallet not found for address:', address);
      return json({ error: 'Wallet not found' }, { status: 404 });
    }

    const teamId = walletData.team_id;
    const teamInternalId = walletData.team_internal_id;

    console.log('[IPN] Found wallet for team:', teamId);

    // Get current team balance and convert crypto amount to USD
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('balance')
      .eq('id', teamId)
      .single();

    if (teamError || !teamData) {
      console.error('[IPN] Error fetching team data:', teamError);
      return json({ error: 'Team not found' }, { status: 404 });
    }

    const currentBalance = parseFloat(teamData.balance) || 0;
    console.log('[IPN] Current team balance fetched:', currentBalance);

    // Validate incoming amount
    const cryptoAmount = parseFloat(amount);
    if (Number.isNaN(cryptoAmount) || cryptoAmount <= 0) {
      console.error('[IPN] Invalid amount received:', amount);
      return json({ error: 'Invalid amount' }, { status: 400 });
    }

    const usdAmount = await convertToUSD(cryptoAmount, currency);
    console.log('[IPN] Converted crypto to USD:', {
      cryptoAmount,
      currency,
      usdAmount,
    });
    if (Number.isNaN(usdAmount) || usdAmount <= 0) {
      console.error('[IPN] Failed to convert amount to USD:', {
        cryptoAmount,
        currency,
        usdAmount,
      });
      return json({ error: 'Conversion error' }, { status: 500 });
    }

    const newBalance = currentBalance + usdAmount;

    console.log('[IPN] Balance calculation:', {
      currentBalance,
      cryptoAmount,
      currency,
      usdAmount,
      newBalance,
    });

    // Create transaction record
    const transactionData = {
      internal_id: txInternalId,
      created_at: new Date().toISOString(),
      team_internal_id: teamInternalId,
      team_id: teamId,
      amount: usdAmount,
      description: `${currency} deposit: ${cryptoAmount} ${currency} = $${usdAmount.toFixed(2)} USD`,
      balance_before: currentBalance,
      balance_after: newBalance,
    };

    // Check if the transaction already exists to avoid duplicate balance updates
    const { data: existingTx, error: existTxError } = await supabase
      .from('transactions')
      .select('internal_id')
      .eq('internal_id', txInternalId)
      .maybeSingle();

    if (existTxError) {
      console.error('[IPN] Error checking existing transaction:', existTxError);
      return json({ error: 'Failed to verify transaction' }, { status: 500 });
    }

    if (existingTx) {
      console.warn(
        '[IPN] Duplicate notification received, transaction already exists. Skipping balance update.'
      );
      return json({ success: true, message: 'Duplicate notification ignored' });
    }

    // Insert new transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .insert([transactionData])
      .select()
      .single();

    if (transactionError) {
      console.error(
        '[IPN] Error inserting transaction record:',
        transactionError
      );
      return json({ error: 'Failed to record transaction' }, { status: 500 });
    }

    console.log('[IPN] Created transaction record:', transaction.internal_id);

    // Attempt to update balance only if the current stored balance matches what we previously read.
    // This avoids double-spending in the (rare) case of concurrent updates.
    const { data: updatedTeam, error: balanceError } = await supabase
      .from('teams')
      .update({ balance: newBalance })
      .eq('id', teamId)
      .eq('balance', currentBalance) // optimistic concurrency check
      .select('balance')
      .maybeSingle();

    if (balanceError || !updatedTeam) {
      console.error(
        '[IPN] Failed to update team balance (may be concurrent update):',
        balanceError
      );
      // We keep the transaction, but signal that balance might need reconciliation.
    } else {
      console.log('[IPN] Updated team balance successfully:', {
        teamId,
        oldBalance: currentBalance,
        newBalance: updatedTeam.balance,
      });
    }

    // Send notification to team devices
    try {
      const notificationTitle = 'Payment Received';
      const notificationMessage = `Received ${cryptoAmount} ${currency} ($${usdAmount.toFixed(2)}) - Balance: $${newBalance.toFixed(2)}`;

      await sendNotification({
        title: notificationTitle,
        message: notificationMessage,
        teamId: teamId,
        data: {
          type: 'payment_received',
          crypto_amount: cryptoAmount.toString(),
          usd_amount: usdAmount.toString(),
          currency: currency,
          address: address,
          transaction_id: id.toString(),
          blockchain_hash: blockchain_hash || '',
          status: status,
          balance_before: currentBalance.toString(),
          balance_after: newBalance.toString(),
        },
      });

      console.log('[IPN] Sent notification to team:', teamId);
    } catch (notificationError) {
      console.error('[IPN] Failed to send notification:', notificationError);
      // Don't fail the entire request if notification fails
    }

    console.log('[IPN] Preparing success response payload');
    // Return success response
    return json({
      success: true,
      message: 'Payment notification processed successfully',
      transaction_id: id.toString(),
      crypto_amount: cryptoAmount,
      usd_amount: usdAmount,
      currency: currency,
      balance_before: currentBalance,
      balance_after: newBalance,
    });
  } catch (error) {
    console.error('[IPN] Error processing payment notification:', error);
    return json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Return basic info for testing
  return json({
    endpoint: 'WestWallet IPN Webhook',
    status: 'active',
    authorized_ip: WESTWALLET_IPN_IP,
  });
}
