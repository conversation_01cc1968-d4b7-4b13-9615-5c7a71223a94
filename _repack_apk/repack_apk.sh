#!/bin/bash

# Enable strict mode
set -euo pipefail
IFS=$'\n\t'

# ====== CONFIG ======
# Default values, override with environment variables
readonly OUTPUT_DIR="${OUTPUT_DIR:-.}"
readonly TMP_DIR="${TMP_DIR:-./tmp_apk}"
readonly SIGNER_JAR="${SIGNER_JAR:-signer.jar}"
readonly KEYSTORE="${KEYSTORE:-${HOME}/.keystore}"
readonly KS_ALIAS="${KS_ALIAS:-defaultalias}"
readonly KS_PASS="${KS_PASS:-}"
readonly KS_KEY_PASS="${KS_KEY_PASS:-${KS_PASS}}"
# ====================

# Check required tools
check_dependencies() {
    local deps=(apktool realpath java)
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            echo "❌ Required tool not found: $dep"
            exit 1
        fi
    done
}

print_help() {
    cat << EOF

Usage: $(basename "$0") <apk_file> -f <asset_file> -n <output_apk_name> [-o <output_dir>] [-k <keystore>] [-s <signer_jar>]

Arguments:
  <apk_file>          Path to the base APK file

Options:
  -f <asset_file>     Path to asset file to embed in the APK (placed into assets/)
  -d <dest_filename>  Destination filename in the APK assets folder (default: basename of asset_file)
  -n <output_apk_name> Name of the final signed APK (required)
  -o <output_dir>     Output directory for the custom APK (default: $OUTPUT_DIR)
  -k <keystore>       Path to keystore file (default: $KEYSTORE)
  -s <signer_jar>     Path to signer JAR file (default: $SIGNER_JAR)
  -a <alias>          Keystore alias (default: $KS_ALIAS)
  -p <password>       Keystore and key password (default: env KS_PASS or prompt)
  -h                  Show help

Environment Variables:
  OUTPUT_DIR          Override default output directory
  TMP_DIR             Override default temporary directory
  SIGNER_JAR          Override default signer JAR path
  KEYSTORE            Override default keystore path
  KS_ALIAS            Override default keystore alias
  KS_PASS             Override default keystore password
  KS_KEY_PASS         Override default key password

Example:
  $(basename "$0") t.apk -f phantomvpn.conf -n phantomvpn.apk -o .
EOF
    exit 1
}

cleanup() {
    if [[ -d "$TMP_DIR" ]]; then
        rm -rf "$TMP_DIR"
    fi
}

# Prompt for password if not provided
get_password() {
    if [[ -z "$KS_PASS" ]]; then
        read -s -p "Enter keystore password: " KS_PASS
        echo
    fi
    if [[ -z "$KS_KEY_PASS" ]]; then
        KS_KEY_PASS="$KS_PASS"
    fi
}

# Set trap for cleanup on script exit
trap cleanup EXIT

# ====== Main Logic ======
main() {
    check_dependencies

    # Validate first argument (APK file)
    [[ $# -lt 1 ]] && { echo "❌ Base APK file is required."; print_help; }
    local BASE_APK="$1"
    shift

    # Parse arguments
    local ASSET_FILE=""
    local DEST_FILENAME=""
    local OUTPUT_APK_NAME=""
    local CUSTOM_OUTPUT_DIR="$OUTPUT_DIR"
    local CUSTOM_KEYSTORE="$KEYSTORE"
    local CUSTOM_SIGNER_JAR="$SIGNER_JAR"
    local CUSTOM_KS_ALIAS="$KS_ALIAS"
    local CUSTOM_KS_PASS="$KS_PASS"
    local CUSTOM_KS_KEY_PASS="$KS_KEY_PASS"

    while getopts ":f:d:n:o:k:s:a:p:h" opt; do
        case $opt in
            f) ASSET_FILE="$OPTARG" ;;
            d) DEST_FILENAME="$OPTARG" ;;
            n) OUTPUT_APK_NAME="$OPTARG" ;;
            o) CUSTOM_OUTPUT_DIR="$OPTARG" ;;
            k) CUSTOM_KEYSTORE="$OPTARG" ;;
            s) CUSTOM_SIGNER_JAR="$OPTARG" ;;
            a) CUSTOM_KS_ALIAS="$OPTARG" ;;
            p) CUSTOM_KS_PASS="$OPTARG"; CUSTOM_KS_KEY_PASS="$OPTARG" ;;
            h) print_help ;;
            \?) echo "❌ Invalid option: -$OPTARG"; print_help ;;
            :) echo "❌ Option -$OPTARG requires an argument."; print_help ;;
        esac
    done

    # Validate inputs
    [[ -z "$ASSET_FILE" ]] && { echo "❌ Path to asset file is required."; print_help; }
    [[ -z "$OUTPUT_APK_NAME" ]] && { echo "❌ Output APK name is required (-n)."; print_help; }
    [[ ! -f "$BASE_APK" ]] && { echo "❌ Base APK not found: $BASE_APK"; exit 1; }
    [[ ! -f "$ASSET_FILE" ]] && { echo "❌ Asset file not found: $ASSET_FILE"; exit 1; }
    [[ ! -f "$CUSTOM_SIGNER_JAR" ]] && { echo "❌ Signer JAR not found: $CUSTOM_SIGNER_JAR"; exit 1; }
    [[ ! -f "$CUSTOM_KEYSTORE" ]] && { echo "❌ Keystore not found: $CUSTOM_KEYSTORE"; exit 1; }

    # Handle password
    if [[ -z "$CUSTOM_KS_PASS" ]]; then
        get_password
        CUSTOM_KS_PASS="$KS_PASS"
        CUSTOM_KS_KEY_PASS="$KS_KEY_PASS"
    fi

    # Derive temporary name from BASE_APK for internal use
    local APK_NAME
    APK_NAME=$(basename "$BASE_APK" .apk)

    # Construct the final output path using the specified name
    local OUTPUT_APK="$CUSTOM_OUTPUT_DIR/$OUTPUT_APK_NAME"

    # Use a temporary name for the repacked APK to avoid naming conflicts
    local TEMP_APK="$CUSTOM_OUTPUT_DIR/temp-$APK_NAME.apk"

    echo "🔄 Processing APK: $APK_NAME"

    # Prepare temporary directories
    rm -rf "$TMP_DIR"
    mkdir -p "$TMP_DIR/$APK_NAME"

    # Only create CUSTOM_OUTPUT_DIR if it's not the current directory
    if [[ "$CUSTOM_OUTPUT_DIR" != "." ]]; then
        mkdir -p "$CUSTOM_OUTPUT_DIR"
    fi

    # Unpack the base APK using apktool
    echo "📦 Unpacking base APK..."
    apktool d -f -r -s "$BASE_APK" -o "$TMP_DIR/$APK_NAME" > /dev/null

    # Add the asset file to the assets folder with the specified destination filename
    # If no destination filename is specified, use the basename of the asset file
    if [[ -z "$DEST_FILENAME" ]]; then
        DEST_FILENAME="$(basename "$ASSET_FILE")"
    fi

    echo "📝 Adding asset: $DEST_FILENAME (from $(basename "$ASSET_FILE"))"
    mkdir -p "$TMP_DIR/$APK_NAME/assets"
    cp "$ASSET_FILE" "$TMP_DIR/$APK_NAME/assets/$DEST_FILENAME"

    # Repack the APK using apktool with a temporary name
    echo "🔨 Repacking APK..."
    apktool b "$TMP_DIR/$APK_NAME" -o "$TEMP_APK"

    # Verify file sizes
    get_file_size() {
        local file="$1"
        if command -v gstat >/dev/null 2>&1; then
            gstat --format="%s" "$file"
        elif [[ "$(uname)" == "Darwin" ]]; then
            stat -f "%z" "$file"
        else
            stat --format="%s" "$file"
        fi
    }

    original_size=$(get_file_size "$BASE_APK")
    new_size=$(get_file_size "$TEMP_APK")

    original_mb=$(echo "scale=2; $original_size/1048576" | bc)
    new_mb=$(echo "scale=2; $new_size/1048576" | bc)

    echo "📊 Original APK size: ${original_mb}MB"
    echo "📊 New APK size: ${new_mb}MB"

    if [ $new_size -lt $(($original_size * 8 / 10)) ]; then
        echo "⚠️  Warning: New APK is significantly smaller than original!"
    fi

    # Sign the APK
    echo "🔑 Signing the APK..."
    java -jar "$CUSTOM_SIGNER_JAR" \
        --ks "$CUSTOM_KEYSTORE" \
        --ksAlias "$CUSTOM_KS_ALIAS" \
        --ksPass "$CUSTOM_KS_PASS" \
        --ksKeyPass "$CUSTOM_KS_KEY_PASS" \
        --apks "$TEMP_APK" > /dev/null

    local SIGNED_APK="${TEMP_APK%.apk}-aligned-signed.apk"
    local IDSIG_FILE="${SIGNED_APK}.idsig"

    # Remove the .idsig file
    rm -f "$IDSIG_FILE"

    # Move the signed APK to the final specified name
    mv "$SIGNED_APK" "$OUTPUT_APK"

    # Remove the temporary repacked APK
    rm -f "$TEMP_APK"

    echo "✅ Custom APK signed: $OUTPUT_APK"
}

main "$@"